import React, { useContext } from 'react';
import { LanguageContext } from '../../context/LanguageProvider';
import { isEmpty } from '../../helpers';
import { useAppContext } from '../../hooks';
import { Ifield } from '../../types';

const textSucess = (text: string) => <p className='vnpt-text-success'>{text}</p>
const textError = (text: string) => <div className='vnpt-text-red-500'>{text}</div>
export default function Validation() {
    const { dataCapture } = useAppContext();

    //get data context language
    const { dictionary } = useContext(LanguageContext);
    const result = dictionary['result']['validationFields']
    const response = dictionary['response']

    const informationAlert = (warningMessage: string[]) => {
        if (warningMessage.length > 0) {
            return <div>
                {warningMessage.map((mes) => textError(response[mes]))}
            </div>
        }
        return <div className='vnpt-text-success'>{response['valid']}</div>
    }
    function renderQualityID(data: any) {
        if (!data) return <p></p>;
        let id_props = JSON.parse(data)
        if (isEmpty(id_props)) {
            return textError(response['blurred'])
        } else {
            for (var i = 0; i < id_props.length; i++) {
                if (id_props[i] < 0.93) {
                    return textError(response['blurred'])
                }
            }
        }
        return textSucess(response['good'])
    }
    function renderQualityIssueDate(data: any) {
        if (data < 0.93) {
            return textError(response['blurred'])
        }
        return textSucess(response['good'])
    }
    function renderExpireWarning(data: string) {
        if (data == "no") {
            return textSucess(response['unexpired'])
        }
        return textError(response['expired'])
    }
    function overrideImage(front: any) {
        if (front) {
            return textError(response['yes'])
        }
        return textSucess(response['no'])
    }
    function renderIsEyeOpen(data: string) {
        if (data == "yes") {
            return textSucess(response['yes'])
        }
        return textError(response['no'])
    }
    function renderBlurFace(data: string) {
        if (data == "no") {
            return textSucess(response['no'])
        }
        return textError(response['yes'])
    }
    function authenticateFace(data: string) {
        if (data == "success") {
            return <div className='vnpt-bg-blur-success vnpt-p-1 vnpt-rounded vnpt-text-center'>
                {textSucess(response['valid'])}
            </div >
        }
        return <div className='vnpt-rounded vnpt-text-center'>
            {textError(response['invalid'])}
        </div>
    }
    function maskedFace(data: string) {
        if (data == 'yes') {
            return <div className='vnpt-rounded vnpt-text-center'>
                {textError(response['yes'])}
            </div>
        }
        return <div className='vnpt-bg-blur-success vnpt-p-1 vnpt-rounded vnpt-text-center'>
            {textSucess(response['no'])}
        </div>

    }
    function documentVerification(front: any, back: any) {
        if (front || back) {
            return textError(response['no_live_shooting'])
        }
        return textSucess(response['live_shooting'])
    }



    function renderBackTypeID(back_type_id: number | null, type_id: number | null) {
        if (back_type_id == type_id) {
            return textSucess(response['identical'])
        }
        return textError(response['different'])
    }
    function renderBackTypeIDError(data: any) {
        // let found = data && data.errors.find((e: any) => e == "Dau vao mat truoc va sau khong cung loai");
        // if (!isEmpty(found)) {
        //     return <div className='vnpt-text-red-500'>Không cùng loại</div>
        // }
        return textError(response['different'])
    }

    // extract data
    const OCR = dataCapture.orc.object;
    const livenessFront = dataCapture.liveness_card_front.object;
    const livenessBack = dataCapture.liveness_card_back.object;
    const livenessFace = dataCapture.liveness_face.object;
    const mask = dataCapture.maskedFaceRes.object



    const templateValidation: Ifield[] = [
        {
            title: result["document_type"],
            value: !isEmpty(OCR) ? textSucess(response['valid']) : textError(response['invalid'])
        },
        {
            title: result["card_blur"],
            value: !isEmpty(OCR) ? isEmpty(OCR.warning) ? textSucess(response['no']) : textError(response['yes']) : '_'
        },
        {
            title: result["quality_id"],
            value: !isEmpty(OCR) ? renderQualityID(OCR.id_probs) : '_'
        },
        {
            title: result["quality_provide_date"],
            value: !isEmpty(OCR) ? renderQualityIssueDate(OCR.issue_date_prob) : '_'
        },
        {
            title: result["expire_warning"],
            value: !isEmpty(OCR) ? renderExpireWarning(OCR.expire_warning) : '_'
        },
        {
            title: result["document_verification"],
            value: (!isEmpty(livenessFront) && !isEmpty(livenessBack)) ? documentVerification(livenessFront.fake_liveness, livenessBack.fake_liveness) : '_'
        },
        {
            title: result["infor_warning"],
            value: !isEmpty(OCR) ? informationAlert(OCR.tampering.warning) : "_"
        },
        {
            title: result["override_image"],
            value: !isEmpty(dataCapture.liveness_card_front) ? !isEmpty(livenessFront) ? overrideImage(livenessFront.face_swapping) : "_" : '_'
        },
        {
            title: result["eyes_open"],
            value: !isEmpty(livenessFace) ? renderIsEyeOpen(livenessFace.is_eye_open) : '_'
        },
        {
            title: result["face_blur"],
            value: !isEmpty(livenessFace) ? renderBlurFace(livenessFace.blur_face) : '_'
        },
        {
            title: result["authenticate_face"],
            value: !isEmpty(livenessFace) ? authenticateFace(livenessFace.liveness) : '_'
        },
        {
            title: result["masked_face"],
            value: !isEmpty(mask) ? maskedFace(mask.masked) : '_'
        },
    ]

    const field = {
        title: result["font_back_document"],
        value: !isEmpty(dataCapture.orc.object) ? renderBackTypeID(dataCapture.orc.object.back_type_id, dataCapture.orc.object.type_id) : renderBackTypeIDError(dataCapture)
    }

    // add a field if it's two side
    if (dataCapture.base64_doc_img.img_back) {
        templateValidation.splice(1, 0, field);
    }

    // const getTemplateDocument = (idDocument: number): Ifield[] => {
    //     if (idDocument === 4) return templateValidation;
    //     return templateValidation;
    // }

    // const dataTemplate = getTemplateDocument(4);
    return (
        <>
            {templateValidation.map((item) => (
                <div key={item.title} className='vnpt-flex vnpt-py-1 sm:vnpt-py-2'>
                    <div className='vnpt-flex-1 vnpt-font-normal vnpt-font-open vnpt-text-sm sm:vnpt-text-base vnpt-text-gray-600'>{item.title}</div>
                    <div className='vnpt-flex-1 vnpt-ml-4'>
                        <div className='vnpt-font-semibold vnpt-font-open vnpt-text-sm sm:vnpt-text-base vnpt-text-gray-900'>{item.value}</div>
                    </div>
                </div>
            ))}
        </>
    )
}
