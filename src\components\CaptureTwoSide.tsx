import React, { useContext, useEffect, useRef, useState } from 'react';
import { LanguageContext } from '../context/LanguageProvider';
import { useAppContext, useUploadImage } from '../hooks';
import { captureProps } from '../types';
import { clearPhoto, getImageReduceSize, handleTurnOnWebCam, stopBothVideoAndAudio } from '../utils';
import ArrowRight from './Icons/ArrowRight';
import ButtonV2 from './Icons/ButtonV2';
import Camera from './Icons/Camera';
import Retake from './Icons/Retake';
import Upload from './Icons/Upload';
import Loading from './Loading';
import StepChecking from './StepChecking';
import ButtonCamera from './Icons/ButtonCamera';
import { Button } from 'antd';

function CaptureTwoSide({ onNextResult, setIsShowModal }: captureProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [streams, setStreams] = useState<MediaStream[]>([]);

  const { handleFileChange, previewURL, setPreviewURL } = useUploadImage();
  const { dataCapture, setDataCapture, dataConfig, setDataConfig } = useAppContext();
  //get data context language
  const { dictionary } = useContext(LanguageContext);

  // check is new cccd
  const hasQR = dataCapture.type_document === 9;

  const dafaultStep: string = dataCapture.othersData.step;
  const [step, setStep] = useState<string>(dafaultStep);

  let imageFront = dataCapture.base64_doc_img.img_front;
  let imageBack = dataCapture.base64_doc_img.img_back;

  function takePiture() {
    if (!canvasRef.current) return;
    if (!videoRef.current) return;
    const context: CanvasRenderingContext2D | null = canvasRef.current.getContext('2d');
    if (videoRef.current.videoWidth && context) {
      canvasRef.current.width = videoRef.current.videoWidth;
      canvasRef.current.height = videoRef.current.videoHeight;
      context.drawImage(videoRef.current, 0, 0, videoRef.current.videoWidth, videoRef.current.videoHeight);

      // get data image from canvas
      const resultb64: string = getImageReduceSize(canvasRef.current, dataConfig.MAX_SIZE_IMAGE);

      if (step === 'first') {
        setDataCapture({
          ...dataCapture,
          base64_doc_img: { img_front: resultb64 },
        });
      }
      if (step === 'second') {
        setDataCapture({
          ...dataCapture,
          base64_doc_img: { img_front: imageFront, img_back: resultb64 },
        });
      }
    } else {
      setDataCapture({
        ...dataCapture,
        base64_doc_img: { img_front: clearPhoto(canvasRef.current) },
      });
    }
    stopBothVideoAndAudio(streams);
  }

  function onNext() {
    if (step === 'first') {
      setDataCapture({ ...dataCapture });
      setStep('second');
      return;
    }
    if (step === 'second') {
      setDataCapture({
        ...dataCapture,
      });
    }
    onNextResult();
  }

  useEffect(() => {
    async function onStartWebcam() {
      setIsLoading(true);
      await handleTurnOnWebCam({
        videoRef: videoRef.current,
        streams: streams,
        turnOnFrontCam: false, // true for turn on front camera
        fakeCamLabel: dataConfig.FAKE_CAM_LABEL,
      });
      setIsLoading(false);
    }
    onStartWebcam();
  }, [dataCapture.base64_doc_img.img_front, dataCapture.base64_doc_img.img_back, step]);

  useEffect(() => {
    if (previewURL && step === 'first') {
      stopBothVideoAndAudio(streams);
      setDataCapture({
        ...dataCapture,
        base64_doc_img: { img_front: previewURL },
      });
    }
    if (previewURL && (step === 'middle' || step === 'second')) {
      stopBothVideoAndAudio(streams);
      setDataCapture({
        ...dataCapture,
        base64_doc_img: {
          img_front: dataCapture.base64_doc_img.img_front,
          img_back: previewURL,
        },
        othersData: { step: 'second' },
      });
      setStep('second');
    }
  }, [previewURL]);

  const buttonsControl = () => {
    let src: string | undefined = undefined;
    if (step === 'first') src = imageFront;
    if (step === 'second') src = imageBack;

    const retake = () => {
      if (step === 'first') {
        setDataCapture({ ...dataCapture, base64_doc_img: { img_front: '' } });
      }
      if (step === 'second') {
        setDataCapture({
          ...dataCapture,
          base64_doc_img: { img_front: imageFront, img_back: '' },
        });
      }
    };
    return (
      <div>
        {src ? (
          <div className="vnpt-flex vnpt-gap-4">
            <div
              onClick={retake}
              className="sm:vnpt-w-52 vnpt-text-center vnpt-py-2 vnpt-px-8 vnpt-border-2 vnpt-border-solid vnpt-border-primary vnpt-text-base vnpt-font-medium vnpt-font-inter vnpt-bg-white vnpt-rounded-full vnpt-text-primary vnpt-cursor-pointer"
            >
              Chụp lại
            </div>
            <div
              onClick={src ? onNext : undefined}
              className="sm:vnpt-w-52 vnpt-text-center vnpt-py-2 vnpt-px-8 vnpt-bg-primary vnpt-rounded-full vnpt-text-base vnpt-font-medium vnpt-font-inter vnpt-text-white vnpt-cursor-pointer"
            >
              {step === 'second' ? 'Tiếp theo' : 'Chụp mặt sau'}
            </div>
          </div>
        ) : (
          <div className="vnpt-cursor-pointer" onClick={takePiture}>
            <ButtonCamera />
          </div>
        )}
      </div>
    );
  };

  const videosControl = () => {
    let src: string | undefined = undefined;
    if (step === 'first') src = imageFront;
    if (step === 'second') src = imageBack;
    return (
      <div className="box vnpt-p-2 vnpt-mt-6">
        <div className="vnpt-mx-auto vnpt-h-60 sm:vnpt-h-auto sm:vnpt-w-96 vnpt-rounded-lg vnpt-min-w-80 vnpt-overflow-hidden vnpt-flex vnpt-justify-center vnpt-items-center">
          {isLoading && <Loading />}
          {!!src ? (
            <div className="output">
              <img className="vnpt-rounded-lg" src={src} id="photo" alt="The screen capture will appear in this box." />
            </div>
          ) : (
            <video className="vnpt-rounded-lg" playsInline ref={videoRef} id="video">
              Video stream not available!
            </video>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className="vnpt-flex vnpt-flex-col vnpt-justify-center vnpt-items-center vnpt-mx-auto vnpt-mt-8 vnpt-max-w-md">
        <p className="vnpt-font-inter vnpt-text-md vnpt-text-gray-700 vnpt-font-bold vnpt-text-center vnpt-mb-4">
          {step === 'first' ? 'Chụp CCCD mặt trước' : 'Chụp CCCD mặt sau'}
        </p>
        <p className="vnpt-text-balance vnpt-text-base vnpt-text-gray-700 vnpt-font-normal vnpt-font-inter vnpt-text-center">
          Đặt mặt trước CCCD nằm giữa khung hình, chụp đủ ánh sáng, không lóa sáng và rõ nét.
        </p>
        <canvas className="vnpt-hidden" ref={canvasRef} id="canvas">
          {' '}
        </canvas>
        {videosControl()}
        <div className="vnpt-mt-20">{streams.length > 0 && buttonsControl()}</div>
      </div>
    </div>
  );
}

export default CaptureTwoSide;
