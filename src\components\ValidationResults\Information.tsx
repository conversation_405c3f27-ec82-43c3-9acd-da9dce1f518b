import React, { useContext } from 'react';
import { LanguageContext } from '../../context/LanguageProvider';
import { isEmpty } from '../../helpers';
import { useAppContext } from '../../hooks';
import { Ifield } from '../../types';

export default function Information() {
    const { dataCapture } = useAppContext();
    //get data context language
    const { dictionary } = useContext(LanguageContext);
    const result = dictionary['result']['informationFields']
    const response = dictionary['response']

    // extract data
    const OCR = dataCapture.orc.object;
    const dataCompare = dataCapture.compare.object;


    function checkInfoDocument(dataCapture: any) {
        if (isEmpty(dataCapture)) {
            return false;
        }
        // extract data response
        const livenessFront = dataCapture.liveness_card_front.object;
        const livenessBack = dataCapture.liveness_card_back.object;

        // check has data object
        if (isEmpty(livenessFront) || isEmpty(livenessBack) || isEmpty(OCR)) {
            return false;
        }

        if (
            livenessFront.face_swapping ||
            livenessFront.fake_liveness ||
            livenessFront.fake_print_photo ||
            livenessBack.face_swapping ||
            livenessBack.fake_liveness ||
            livenessBack.fake_print_photo
        ) {
            return false;
        }

        if (!isEmpty(OCR) && OCR.general_warning.length > 0) {
            return false;
        }
        return true;
    }

    // show information 
    const checkValid = (flag: boolean) => {
        if (flag) return <p className='vnpt-text-success'>{response['valid']}</p>;
        if (!isEmpty(OCR) && OCR.general_warning.length > 0) {
            return <div>
                {OCR.general_warning.map((mes) => <p className='vnpt-text-red-500'>{response[mes]}</p>)}
            </div>
        } else {
            return <p className='vnpt-text-red-500'>{response['invalid']}</p>;
        }
    }
    function compareFace(data: any, text?: string) {
        if (data.msg === "NOMATCH" || data.msg === "NOTHING") {
            return <p className='vnpt-text-red-500' >{response['faces_mismatch']}</p>
        }
        return <div className='vnpt-bg-blur-success vnpt-p-1 vnpt-rounded vnpt-text-center'>
            <p className='vnpt-text-success'>{!!text ? text : `${response['face_matching']} ${data.prob}`}%</p>
        </div>
    }




    let template: Ifield[] = [
        {
            title: result["document"],
            value: checkValid(checkInfoDocument(dataCapture))
        },
        {
            title: result["id_number"],
            value: !isEmpty(OCR) ? OCR.id : '_'
        },
        {
            title: result["full_name"],
            value: !isEmpty(OCR) ? OCR.name : '_'
        },
        {
            title: result["date_of_birth"],
            value: !isEmpty(OCR) ? OCR.birth_day : '_'
        },
        {
            title: result["permanent_residence"],
            value: !isEmpty(OCR) ? OCR.recent_location : '_'
        },
        {
            title: result["gender"],
            value: !isEmpty(OCR) ? OCR.gender : '_'
        },
        {
            title: result["provide_date"],
            value: !isEmpty(OCR) ? OCR.issue_date : '_'
        },
        {
            title: result["exprire_date"],
            value: !isEmpty(OCR) ? OCR.valid_date : '_'
        },
        {
            title: result["provide_location"],
            value: !isEmpty(OCR) ? OCR.issue_place : '_'
        },
        {
            title: result["compare"],
            value: !isEmpty(dataCompare) ? compareFace(dataCompare) : "_"
        }
    ];

    // update result by QR
    if (dataCapture.QRscan) {
        template.push(
            {
                title: result["result"],
                value: !isEmpty(dataCompare) ? compareFace(dataCompare, "OCR khớp QR Code") : "_"
            }
        )
    }

    return (
        <>
            {template.map((item) => (
                <div key={item.title} className='vnpt-flex vnpt-py-1 sm:vnpt-py-2'>
                    <div className='vnpt-flex-1 vnpt-font-normal vnpt-font-open vnpt-text-sm sm:vnpt-text-base vnpt-text-gray-600'>{item.title}</div>
                    <div className='vnpt-flex-1 vnpt-ml-4'>
                        <div className='vnpt-font-semibold vnpt-font-open vnpt-text-sm sm:vnpt-text-base vnpt-text-gray-900'>{item.value}</div>
                    </div>
                </div>
            ))}
        </>
    )
}
