import React from 'react'

type Props = {}

export default function License({ }: Props) {
    return (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M13.8477 22.1685H3.53195C2.59904 22.1685 1.83943 21.4121 1.83943 20.4833V5.79307H30.0408V18.2893C30.0408 18.7949 30.4527 19.2051 30.9605 19.2051C31.4683 19.2051 31.8803 18.7949 31.8803 18.2893V3.5768C31.8803 1.63675 30.2967 0.0600586 28.3483 0.0600586H3.53195C1.58357 0.0599976 0 1.63676 0 3.5768V20.4833C0 22.4233 1.58358 24 3.53195 24H13.8477C14.3571 24 14.7675 23.5894 14.7675 23.0842C14.7675 22.5787 14.3555 22.1685 13.8477 22.1685ZM2.619 23.6053C2.90881 23.6894 3.21517 23.7345 3.53195 23.7345H13.8477C14.2095 23.7345 14.5008 23.4431 14.5008 23.0842C14.5008 23.0038 14.4861 22.9267 14.4592 22.8556C14.4861 22.9267 14.5007 23.0037 14.5007 23.0841C14.5007 23.4431 14.2094 23.7344 13.8477 23.7344H3.53189C3.21513 23.7344 2.90879 23.6894 2.619 23.6053ZM1.63223 20.9613C1.59336 20.8082 1.5727 20.6481 1.5727 20.4832V5.52748H30.3074V5.52755H1.57277V20.4833C1.57277 20.6481 1.59339 20.8083 1.63223 20.9613ZM30.7486 18.9046C30.815 18.9273 30.8863 18.9396 30.9605 18.9396C31.321 18.9396 31.6136 18.6483 31.6136 18.2893V3.5768C31.6136 3.36009 31.5922 3.14829 31.5514 2.94336C31.5922 3.14827 31.6135 3.36006 31.6135 3.57674V18.2893C31.6135 18.6482 31.3209 18.9395 30.9604 18.9395C30.8863 18.9395 30.815 18.9272 30.7486 18.9046ZM1.83943 3.5768C1.83943 2.64791 2.59904 1.89157 3.53195 1.89157H28.3482C29.2811 1.89157 30.0408 2.64791 30.0408 3.5768V3.96155H1.83943V3.5768ZM1.57277 3.5768C1.57277 2.50127 2.45177 1.62605 3.53195 1.62605H28.3482C28.4811 1.62605 28.6109 1.63928 28.7365 1.6645C28.6109 1.63925 28.4811 1.62599 28.3482 1.62599H3.53189C2.4517 1.62599 1.5727 2.5012 1.5727 3.57674V4.22701H1.57277V3.5768ZM19.2058 14.9309H28.3486C28.8736 14.9309 29.3017 14.5064 29.3017 13.9806C29.3017 13.4547 28.8736 13.0303 28.3486 13.0303H19.2058C18.6809 13.0303 18.2527 13.4547 18.2527 13.9806C18.2527 14.5064 18.6809 14.9309 19.2058 14.9309ZM9.33366 11.2848C9.70242 11.2848 10.0003 10.9882 10.0003 10.621C10.0003 10.2538 9.70242 9.9572 9.33366 9.9572C8.9649 9.9572 8.66699 10.2538 8.66699 10.621C8.66699 10.9882 8.9649 11.2848 9.33366 11.2848ZM12.0003 10.621C12.0003 12.088 10.807 13.2762 9.33366 13.2762C7.86033 13.2762 6.66699 12.088 6.66699 10.621C6.66699 9.15401 7.86033 7.96581 9.33366 7.96581C10.807 7.96581 12.0003 9.15401 12.0003 10.621ZM9.33301 13.6079C8.49554 13.6079 7.3343 13.8733 6.36125 14.3908C5.42012 14.8913 4.33301 15.815 4.33301 17.2588V19.5821H14.333V17.2588C14.333 15.815 13.2459 14.8913 12.3048 14.3908C11.3317 13.8733 10.1705 13.6079 9.33301 13.6079ZM6.33301 17.2588C6.33301 16.9369 6.5784 16.533 7.30352 16.1473C7.99672 15.7787 8.83548 15.5993 9.33301 15.5993C9.83054 15.5993 10.6693 15.7787 11.3625 16.1473C12.0876 16.533 12.333 16.9369 12.333 17.2588V17.5907H6.33301V17.2588ZM19.2058 11.0293H23.1242C23.6491 11.0293 24.0773 10.6048 24.0773 10.079C24.0773 9.55315 23.6491 9.12873 23.1242 9.12873H19.2058C18.6809 9.12873 18.2527 9.55315 18.2527 10.079C18.2527 10.6048 18.6809 11.0293 19.2058 11.0293ZM28.3484 9.01593H25.7361C25.2111 9.01593 24.783 9.44036 24.783 9.9662C24.783 10.492 25.2111 10.9165 25.7361 10.9165H28.3484C28.8733 10.9165 29.3014 10.492 29.3014 9.9662C29.3014 9.44036 28.8733 9.01593 28.3484 9.01593Z" fill="white" />
            <path d="M30.1295 23.4813L29.2587 22.6105L28.573 18.8389C28.414 17.9662 27.6559 17.3333 26.769 17.3333H19.8977C19.0108 17.3333 18.2527 17.9662 18.0937 18.8389L17.408 22.6105L16.5372 23.4813C16.1907 23.8274 16 24.288 16 24.778V27.4166C16 27.6696 16.2053 27.8749 16.4583 27.8749H30.2083C30.4613 27.8749 30.6667 27.6696 30.6667 27.4166V24.778C30.6667 24.288 30.476 23.8274 30.1295 23.4813ZM18.9957 19.0025C19.075 18.5662 19.4545 18.2499 19.8977 18.2499H26.769C27.2122 18.2499 27.5917 18.5662 27.671 19.0025L28.2843 22.3749H18.3824L18.9957 19.0025ZM18.9792 26.0416C18.5997 26.0416 18.2917 25.7336 18.2917 25.3541C18.2917 24.9746 18.5997 24.6666 18.9792 24.6666C19.3587 24.6666 19.6667 24.9746 19.6667 25.3541C19.6667 25.7336 19.3587 26.0416 18.9792 26.0416ZM27.6875 26.0416C27.308 26.0416 27 25.7336 27 25.3541C27 24.9746 27.308 24.6666 27.6875 24.6666C28.067 24.6666 28.375 24.9746 28.375 25.3541C28.375 25.7336 28.067 26.0416 27.6875 26.0416Z" fill="white" />
            <path d="M16.917 28.7915V29.7082C16.917 29.9612 17.1223 30.1665 17.3753 30.1665H19.667C19.92 30.1665 20.1253 29.9612 20.1253 29.7082V28.7915H16.917Z" fill="white" />
            <path d="M26.542 28.7915V29.7082C26.542 29.9612 26.7473 30.1665 27.0003 30.1665H29.292C29.545 30.1665 29.7503 29.9612 29.7503 29.7082V28.7915H26.542Z" fill="white" />
        </svg>
    )
}
