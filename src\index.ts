import React from 'react';
import ReactDOM from 'react-dom';
// import './index.css';
import { App } from "./components/App";
import UserService from "./service/Users";

declare global {
  interface Window {
    SDK: any;
  }

  var APP_ENV: any;
}

class SDK {
  protected user: UserService;
  constructor() {
    this.user = new UserService();
  }

  public getVersion(): string {
    return "v1";
  }

  public getBaseURL(): string {
    return APP_ENV.API_BASE_URL;
  }

  public launch(dataConfig: any): void {
    ReactDOM.render(React.createElement(App, dataConfig), document.getElementById('ekyc_sdk_intergrated'));
  }
}

window.SDK = new SDK();
