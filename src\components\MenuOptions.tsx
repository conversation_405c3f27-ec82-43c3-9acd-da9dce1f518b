import React from 'react'
import DocumentSelect from './DocumentSelect'
import { DocumentProps } from '../types'

type Props = {
    documentIds: DocumentProps[]
    handleChooseDocument: (item: DocumentProps) => void,
    title: string,
    subTitle: string,
}

function MenuOptions({ documentIds, handleChooseDocument, title, subTitle }: Props) {
    return (
        <div className="vnpt-max-w-sm vnpt-mt-8 vnpt-mx-auto">
            <div>
                <p className="vnpt-font-semibold vnpt-text-gray-0 vnpt-text-lg vnpt-text-center">{title}</p>
                <p className='vnpt-mt-1 vnpt-font-normal vnpt-text-gray-0 vnpt-text-center'>{subTitle}</p>
            </div>
            <div className="vnpt-content-center vnpt-mt-14">
                {documentIds.map((item, index) => (
                    <DocumentSelect key={index} item={item} action={handleChooseDocument} />
                ))}
            </div>
        </div>
    )
}

export default MenuOptions
