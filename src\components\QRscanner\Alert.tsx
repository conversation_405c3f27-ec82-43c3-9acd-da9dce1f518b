import React from 'react';
import AlertICon from '../Icons/Alert';
type Props = {
  closeAlert: any;
  dictionary: any;
};
function Alert({ closeAlert, dictionary }: Props) {
  return (
    <>
      <div className="vnpt-flex vnpt-w-[300px] vnpt-mt-[20%] vnpt-mx-auto vnpt-gap-2 vnpt-py-6 vnpt-bg-white vnpt-flex-col vnpt-items-center vnpt-shadow-sm vnpt-rounded-[10px]">
        <div className="vnpt-flex vnpt-flex-col vnpt-items-center">
          <AlertICon />
          <p className="vnpt-text-[#0F2B3B] vnpt-font-bold vnpt-text-lg vnpt-mt-1">
            {dictionary['noti']}
          </p>
        </div>
        <div className="vnpt-text-center vnpt-space-y-1 vnpt-mt-1">
          <p className=" vnpt-text-sm vnpt-font-normal vnpt-text-[#0F2B3B]">
            {dictionary['QR_invalid']}
          </p>
          <p className=" vnpt-text-sm vnpt-font-normal vnpt-text-[#0F2B3B]">
            {dictionary['QR_error_mes']}
          </p>
        </div>
        <div className="vnpt-h-[1px] vnpt-w-full vnpt-my-2 vnpt-bg-[#E8E8E8]"></div>
        <div className="vnpt-w-[90%] vnpt-cursor-pointer">
          <div
            onClick={closeAlert}
            className="vnpt-rounded-md vnpt-bg-[#18D696] vnpt-mx-auto vnpt-text-center vnpt-py-2"
          >
            <p className="vnpt-text-[#142730] vnpt-font-bold vnpt-text-base">
              {dictionary['agree']}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}

export default Alert;
