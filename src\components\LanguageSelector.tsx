import React, { useCallback, useContext, useMemo } from 'react';

import { LanguageContext } from '../context/LanguageProvider';
import { languageOptions } from '../languages';

function LanguageSelector() {
  const { userLanguage, userLanguageChange } = useContext(LanguageContext);
  // set selected language by calling context method
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) =>
    userLanguageChange(e.target.value);

  return (
    <div className="vnpt-w-28 vnpt-ml-auto vnpt-px-2">
      <select
        onChange={handleLanguageChange}
        value={userLanguage}
        className="vnpt-bg-gray-50 vnpt-border vnpt-border-gray-300 vnpt-text-gray-900 vnpt-text-sm vnpt-rounded-lg vnpt-block vnpt-w-full vnpt-p-2"
      >
        {Object.entries(languageOptions).map(([id, name]) => (
          <option key={id} value={id}>
            {name as string}
          </option>
        ))}
      </select>
    </div>
  );
}
export default React.memo(LanguageSelector);
