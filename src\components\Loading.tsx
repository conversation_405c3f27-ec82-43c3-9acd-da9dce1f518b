import React, { useEffect, useState } from 'react';
type Props = {
  className?: string;
  hasText?: boolean;
};
export default function Loading({ className = 'vnpt-w-12 vnpt-h-12 vnpt-bg-primary vnpt-z-20', hasText }: Props) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((prevProgress) => {
        if (prevProgress >= 100) {
          return 0; // Reset về 0 để chạy lại
        }
        return prevProgress + 1;
      });
    }, 30); // Tốc độ loading

    return () => {
      clearInterval(timer);
    };
  }, []);

  // if (hasText)
  //   return (
  //     <div className="vnpt-flex vnpt-flex-col vnpt-items-center vnpt-gap-2 vnpt-relative">
  //       {/* Circle with blue dot */}
  //       <div className="vnpt-flex vnpt-flex-col vnpt-items-center vnpt-gap-4 vnpt-relative">
  //         <div className={`${className} vnpt-bg-transparent vnpt-absolute`}>
  //           <div className={`one ${className}`}></div>
  //           <div className={`two ${className}`}></div>
  //         </div>
  //       </div>

  //       {/* Text */}
  //       <div className="vnpt-absolute vnpt-top-24">
  //         <div className="text-gray-700 text-sm vnpt-text-center">Đang xử lý dữ liệu</div>
  //         <div className="vnpt-w-48 vnpt-h-1.5 vnpt-bg-gray-200 vnpt-rounded-full vnpt-overflow-hidden vnpt-mt-2">
  //           <div
  //             className="vnpt-h-full vnpt-bg-primary vnpt-rounded-full vnpt-transition-all vnpt-duration-100 vnpt-ease-linear"
  //             style={{ width: `${progress}%` }}
  //           ></div>
  //         </div>
  //       </div>
  //     </div>
  //   );

  return (
    <div className={`${className} vnpt-bg-transparent vnpt-absolute`}>
      <div className={`one ${className}`}></div>
      <div className={`two ${className}`}></div>
    </div>
  );
}
