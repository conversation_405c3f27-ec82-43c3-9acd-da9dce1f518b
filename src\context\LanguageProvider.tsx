import React, { useState, createContext, useContext, useEffect } from 'react';

import { languageOptions, dictionaryList } from '../languages';
import { ContextTypeLanguage } from '../types';
import { useAppContext } from '../hooks';

// create the language context with default selected language
const initialContextLanguage = {
    userLanguage: 'vi',
    dictionary: dictionaryList.vi,
    userLanguageChange: () => { }
}
export const LanguageContext = createContext<ContextTypeLanguage>(initialContextLanguage);

// it provides the language context to app
export default function LanguageProvider({ children }: { children: React.ReactNode }) {
    const defaultLanguage = window.localStorage.getItem('language');
    const { dataConfig } = useAppContext()
    const [userLanguage, setUserLanguage] = useState<string>("vi");

    const provider = {
        userLanguage,
        dictionary: dictionaryList[userLanguage],
        userLanguageChange: (selected: string) => {
            const newLanguage = languageOptions[selected] ? selected : 'vi'
            setUserLanguage(newLanguage);
            window.localStorage.setItem('language', newLanguage);
        }
    };

    useEffect(() => {
        if (defaultLanguage) {
            setUserLanguage(defaultLanguage)
        } else {
            setUserLanguage(dataConfig.DEFAULT_LANGUAGE)
        }
    }, [dataConfig.DEFAULT_LANGUAGE])

    return (
        <LanguageContext.Provider value={provider}>
            {children}
        </LanguageContext.Provider>
    );
};
