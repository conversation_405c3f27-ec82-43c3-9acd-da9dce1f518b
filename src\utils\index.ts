import { ControlWebcamProps, dataConfigType, FakeCamLabel, ResponseDataCapture } from "../types";
import imageCompression from 'browser-image-compression';

// start verify face
export async function drawImageOVal(thresholdX: number, thresholdY: number, video: HTMLVideoElement, canvas: any) {
  return new Promise(function (resolve) {
    if (video) {
      canvas.width = video.videoWidth * thresholdX;
      canvas.height = video.videoHeight * thresholdY;
      var img = new Image();
      img.crossOrigin = "anonymous";
      let sx = ((1 - thresholdX) / 2) * video.videoWidth;
      let sy = ((1 - thresholdY) / 2) * video.videoHeight;
      let sw = video.videoWidth * thresholdX;
      let sh = video.videoHeight;
      let dx = 0;
      let dy = 0;
      let dw = video.videoWidth * thresholdX;
      let dh = video.videoHeight;
      if (sw < 0) {
        sx += sw;
        sw = Math.abs(sw);
      }
      if (sh < 0) {
        sy += sh;
        sh = Math.abs(sh);
      }
      if (dw < 0) {
        dx += dw;
        dw = Math.abs(dw);
      }
      if (dh < 0) {
        dy += dh;
        dh = Math.abs(dh);
      }
      const x1 = Math.max(sx, 0);
      const x2 = Math.min(sx + sw, video.videoWidth);
      const y1 = Math.max(sy, 0);
      const y2 = Math.min(sy + sh, video.videoHeight);
      const w_ratio = dw / (x2 - x1);
      const h_ratio = dh / (y2 - y1);
      canvas
        .getContext("2d")
        .drawImage(
          video,
          x1,
          y1,
          x2 - x1,
          y2 - y1,
          sx < 0 ? dx - sx * w_ratio : dx,
          sy < 0 ? dy - sy * h_ratio : dy,
          (x2 - x1) * w_ratio,
          (y2 - y1) * h_ratio
        );
      resolve(canvas);
    }
  });
}
// end verify face


export function manageStream(streamVideos: MediaStream[], stream: MediaStream) {
  if (stream) {
    streamVideos.push(stream)
  }
}

export const isiPhone = () => {
  return /iPhone/i.test(navigator.userAgent);
}




function normalizeFakeCamLabel(fakeCamLabel: FakeCamLabel): RegExp[] {
  if (typeof fakeCamLabel === 'string') {
    return [new RegExp(fakeCamLabel, 'i')];
  } else if (Array.isArray(fakeCamLabel)) {
    return fakeCamLabel.map(label => new RegExp(label, 'i'));
  } else if (fakeCamLabel instanceof RegExp) {
    return [fakeCamLabel];
  } else {
    throw new Error('Invalid FAKE_CAM_LABEL type');
  }
}
export async function handleTurnOnWebCam({ videoRef, cb, streams, turnOnFrontCam, fakeCamLabel }: ControlWebcamProps) {
  if (!videoRef) return;
  try {
    // Request access to the camera to ensure device labels are available
    // await navigator.mediaDevices.getUserMedia({ video: true });

    let devices = [];
    devices = await navigator.mediaDevices.enumerateDevices(); // get list devices input

    // check fake camera
    // devices.forEach(device => {
    //   if (device.kind === 'videoinput') {
    //     // // Check if the label contains keywords like "Virtual", "Fake", or specific software names
    //     // if (/virtual|fake|obs/i.test(device.label)) {
    //     //   alert('Phát hiện camera không hợp lệ. Vui lòng kiểm tra lại thiết bị của bạn!');
    //     //   window.location.reload();
    //     //   return;
    //     // }
    //     if (fakeCamLabel) {
    //       const fakeCamRegexes = normalizeFakeCamLabel(fakeCamLabel);
    //       const isFakeCam = fakeCamRegexes.some(regex => regex.test(device.label));
    //       if (isFakeCam) {
    //         alert('Phát hiện camera không hợp lệ. Vui lòng kiểm tra lại thiết bị của bạn!');
    //         window.location.reload();
    //         return;
    //       }
    //     }
    //   }
    // });

    // Lọc danh sách các thiết bị video và sắp xếp theo chất lượng từ thấp đến cao
    const videoDevices = devices.filter(device => (device.kind === 'videoinput' && (device.label.toLowerCase().includes('back') || device.label.toLowerCase().includes('sau')))).sort((a, b) => {
      if (a.label > b.label) {
        return 1;
      }
      if (a.label < b.label) {
        return -1;
      }
      return 0;
    });

    // Lấy deviceId của camera sau hoặc camera có chất lượng cao nhất
    const deviceId = videoDevices.length > 0 ? videoDevices[0].deviceId : null;

    // Cấu hình constraints để lấy stream video từ camera sau hoặc camera có chất lượng cao nhất
    const windowWidth = Math.min(window.screen.width, window.outerWidth);

    // check to open front camera to validate face on mobile
    const constraintsForMobile = turnOnFrontCam ? {
      audio: false,
      video: {
        facingMode: "user",
        width: { ideal: 720 },
        aspectRatio: 4 / 3,
      },
    } : {
      audio: false,
      video: {
        facingMode: "environment",
        aspectRatio: 16 / 9,
        width: {
          ideal: 1920,
        },
        height: {
          ideal: 1080,
        },
        deviceId: deviceId ? deviceId : null
      },
    }
    const constraintsForDesktop = {
      video: {
        facingMode: "user",
        width: { ideal: 1280 },
        aspectRatio: 16 / 9,
      },
      audio: false,
    };
    let constraintsUpdate: any = (windowWidth < 1100) ? constraintsForMobile : constraintsForDesktop

    // Lấy stream từ camera với constraints đã cấu hình
    let stream = null;
    try {
      stream = await navigator.mediaDevices.getUserMedia(constraintsUpdate);
    } catch (err) {
      console.error('Failed to get user media:', err);
      throw new Error('Failed to get user media');
    }

    // Hiển thị video từ stream lấy được trên trình duyệt
    videoRef.srcObject = stream;
    if (stream) {
      manageStream(streams, stream);
    }
    if (cb) {
      cb();
    }
    await videoRef.play();

  } catch (error) {
    console.error(error);
    alert('Camera không được hỗ trợ, vui lòng cấp quyền truy cập camera hoặc kiểm tra lại thiết bị của bạn!');
    window.location.reload();
  }
}

// stop both mic and camera
export function stopBothVideoAndAudio(streams: MediaStream[]) {
  for (let index = 0; index < streams.length; index++) {
    const element = streams[index];
    element.getTracks().forEach(function (track) {
      if (track.readyState == "live") {
        track.stop();
      }
    });
  }
}

// stop only camera
export function stopVideoOnly(streams: MediaStream[]) {
  streams.forEach((stream) => {
    stream.getTracks().forEach((track) => {
      if (track.readyState == 'live' && track.kind === 'video') {
        track.stop();
      }
    })
  })
}

// stop only mic
export function stopAudioOnly(stream: MediaStream) {
  stream.getTracks().forEach((track) => {
    if (track.readyState == 'live' && track.kind === 'audio') {
      track.stop();
    }
  });
}

export function clearPhoto(canvas: HTMLCanvasElement | null): string {
  if (!canvas) return "";
  const context: CanvasRenderingContext2D | null = canvas.getContext("2d");
  if (!context) return "";
  context.fillStyle = "#CBCBCB";
  context.fillRect(0, 0, canvas.width, canvas.height);
  const emptyCanvas = canvas.toDataURL("image/jpeg");
  return emptyCanvas;
}

function sayswho() {
  var ua = navigator.userAgent;
  var tem;
  var M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
  if (/trident/i.test(M[1])) {
    tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
    return 'IE ' + (tem[1] || '');
  }
  if (M[1] === 'Chrome') {
    tem = ua.match(/\b(OPR|Edge)\/(\d+)/);
    if (tem != null) return tem.slice(1).join(' ').replace('OPR', 'Opera');
  }
  M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
  if ((tem = ua.match(/version\/(\d+)/i)) != null) M.splice(1, 1, tem[1]);
  return M.join('-');
}
function genUuidv4() {
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
  localStorage.setItem('ekyc_uuid', uuid);
  return uuid;
}
export function defineTransaction(version: string) {
  const PREFIX = "WEB-SDK";
  const TIME = new Date().getTime();
  const VERSION = version;
  // let userAgent = navigator.userAgent;
  let browserName = sayswho();
  let transactionID = null;
  let uuidv4 = localStorage.getItem('ekyc_uuid') ? localStorage.getItem('ekyc_uuid') : genUuidv4();
  transactionID = PREFIX + "_" + browserName + "_" + VERSION + "_" + uuidv4 + "_" + TIME;
  return transactionID;
}

// export const initSDK = async () => {
//   try {
//     const script = document.createElement('script');
//     script.src = 'https://cdn.jsdelivr.net/gh/cycfsabo/vnpt-ekyc-sdk@main/dist/VNPTBrowserSDKAppV4.0.0.js';
//     script.async = true;
//     document.body.appendChild(script);

//     await new Promise((resolve, reject) => {
//       script.onload = resolve;
//       script.onerror = reject;
//     });

//     // Thư viện đã được tải thành công và sẵn sàng sử dụng
//     // Bạn có thể tiếp tục viết mã của mình ở đây và sử dụng các hàm từ thư viện
//     // Ví dụ:
//     const sdk = await (window as any).FaceVNPTBrowserSDK;
//     sdk.init();
//     return sdk;
//     // ... Gọi các hàm khác từ thư viện
//   } catch (error) {
//     console.error('Failed to load the library:', error);
//   }
// }

// export const initVNPTQRBrowserSDK = async () => {
//   try {
//     const script = document.createElement('script');
//     script.src = './lib/VNPTQRBrowserApp.js';
//     script.async = true;
//     document.body.appendChild(script);

//     await new Promise((resolve, reject) => {
//       script.onload = resolve;
//       script.onerror = reject;
//     });

//     // Thư viện đã được tải thành công và sẵn sàng sử dụng
//     // Bạn có thể tiếp tục viết mã của mình ở đây và sử dụng các hàm từ thư viện
//     // Ví dụ:
//     const sdk = await (window as any).VNPTQRBrowserSDK;
//     sdk.createInstance();
//     return sdk;
//     // ... Gọi các hàm khác từ thư viện
//   } catch (error) {
//     console.error('Failed to load the library:', error);
//   }
// }
export const initZxing = async () => {
  try {
    const script = document.createElement('script');
    script.src = './lib/VNPTQRUpload.js';
    script.async = true;
    document.body.appendChild(script);

    await new Promise((resolve, reject) => {
      script.onload = resolve;
      script.onerror = reject;
    });

    // Thư viện đã được tải thành công và sẵn sàng sử dụng
    // Bạn có thể tiếp tục viết mã của mình ở đây và sử dụng các hàm từ thư viện
    // Ví dụ:
    const sdk = await (window as any).ZXing;
    return sdk;
    // ... Gọi các hàm khác từ thư viện
  } catch (error) {
    console.error('Failed to load the library:', error);
  }
}

export const getImageReduceSize = (canvas: HTMLCanvasElement, maxSizeMB: number = 1) => {
  // get data image from canvas
  let quality = 0.9;
  let resultb64: string = canvas.toDataURL('image/jpeg', quality);
  let imageSize = (resultb64.length * (3 / 4)) / (1024 * 1024); // size in MB

  while (imageSize > maxSizeMB && quality > 0.1) {
    quality -= 0.1;
    resultb64 = canvas.toDataURL('image/jpeg', quality);
    imageSize = (resultb64.length * (3 / 4)) / (1024 * 1024); // update size in MB
  }

  return resultb64;
}

export const onShowResult = async (dataConfig: dataConfigType, dataCapture: ResponseDataCapture) => {
  if (typeof dataConfig.CALL_BACK_END_FLOW === 'function') {
    try {
      dataConfig.CALL_BACK_END_FLOW({
        type_document: dataCapture.type_document,
        liveness_card_front: dataCapture.liveness_card_front,
        liveness_card_back: dataCapture.liveness_card_back,
        ocr: dataCapture.orc,
        liveness_face: dataCapture.liveness_face,
        masked: dataCapture.maskedFaceRes,
        hash_img: dataCapture.hash_img,
        compare: dataCapture.compare,
        base64_doc_img: {
          img_front: dataCapture.base64_doc_img.img_front,
          img_back: dataCapture.base64_doc_img.img_back
        },
        base64_face_img: {
          img_face_far: dataCapture.base64_face_img.img_face_far,
          img_face_near: dataCapture.base64_face_img.img_face_near,
        },
        data_hash_document: dataCapture.data_hash_document,
        qrCode: dataCapture.QRscan,
        videoRecorded: dataCapture.videoRecorded
      });
    } catch (error) {
      console.error('Error executing CALL_BACK_END_FLOW:', error);
    }
  } else {
    console.warn('CALL_BACK_END_FLOW is not a function.');
  }
};
export const onShowDocumentResult = async (dataConfig: dataConfigType, dataCapture: ResponseDataCapture) => {
  if (typeof dataConfig.CALL_BACK_DOCUMENT_RESULT === 'function') {
    try {
      // compress images

      await dataConfig.CALL_BACK_DOCUMENT_RESULT({
        type_document: dataCapture.type_document,
        liveness_card_front: dataCapture.liveness_card_front,
        liveness_card_back: dataCapture.liveness_card_back,
        ocr: dataCapture.orc,
        liveness_face: dataCapture.liveness_face,
        masked: dataCapture.maskedFaceRes,
        hash_img: dataCapture.hash_img,
        compare: dataCapture.compare,
        base64_doc_img: {
          img_front: dataCapture.base64_doc_img.img_front,
          img_back: dataCapture.base64_doc_img.img_back
        },
        base64_face_img: {
          img_face_far: dataCapture.base64_face_img.img_face_far,
          img_face_near: dataCapture.base64_face_img.img_face_near,
        },
        data_hash_document: dataCapture.data_hash_document,
        qrCode: dataCapture.QRscan,
        videoRecorded: dataCapture.videoRecorded
      });
    } catch (error) {
      console.error('Error executing CALL_BACK_DOCUMENT_RESULT:', error);
    }
  } else {
    console.warn('CALL_BACK_DOCUMENT_RESULT is not a function.');
  }
};

export function changeThemeDefault(dataConfig: dataConfigType) {
  return {
    __html: `:root {
      --vnpt-primary-color: ${dataConfig.CUSTOM_THEME.PRIMARY_COLOR || "#18d696"};
      --vnpt-text-color-default: ${dataConfig.CUSTOM_THEME.TEXT_COLOR_DEFAULT || "#ffffff"};
      --vnpt-background-sdk: ${dataConfig.CUSTOM_THEME.BACKGROUND_COLOR || "#0F2B3B"};
    }`
  }
}

export async function startRecording(stream: MediaStream, duration: number): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const mediaRecorder = new MediaRecorder(stream);
    const chunks: BlobPart[] = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.push(event.data);
      }
    };

    mediaRecorder.onstop = () => {
      const videoBlob = new Blob(chunks, { type: 'video/webm' });
      resolve(videoBlob);
    };

    mediaRecorder.onerror = (error) => {
      reject(error);
    };

    mediaRecorder.start();

    // Dừng quay sau thời gian `duration`
    setTimeout(() => {
      mediaRecorder.stop();
    }, duration);
  });
}


