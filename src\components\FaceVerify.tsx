import bodymovin from 'lottie-web';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { DataLivenessImg, compareFaceMobile, liveness3DWeb, maskedFaceMobile, uploadImage } from '../api';
import { LIST_DOCUMENT_ID, VERSION } from '../constants';
import { LanguageContext } from '../context/LanguageProvider';
import { b64toBlob } from '../helpers';
import { useAppContext } from '../hooks';
import { IDataHash, ModalType, TYPE_DOCUMENT } from '../types';
import {
  defineTransaction,
  drawImageOVal,
  getImageReduceSize,
  handleTurnOnWebCam,
  onShowResult,
  startRecording,
  stopBothVideoAndAudio,
} from '../utils';
import Loading from './Loading';
import StepChecking from './StepChecking';
const PROCESSING_DELAY = 1000;

const MESSAGE_FACE = {
  FIT: 'FIT',
  TO_THE_RIGHT: 'TO_THE_RIGHT',
  TO_THE_LEFT: 'TO_THE_LEFT',
  TO_THE_BOTTOM: 'TO_THE_BOTTOM',
  TO_THE_TOP: 'TO_THE_TOP',
  NOT_STRAIGHT: 'NOT_STRAIGHT',
  TOO_FAR: 'TOO_FAR',
  TOO_NEAR: 'TOO_NEAR',
  INVALID: 'INVALID',
  NO_FACE: 'NO_FACE',
  TIME_EXCEED: 'TIME_EXCEED',
};

type faceVerifyProps = {
  dataHash: IDataHash;
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  faceSDK: any;
  typeDocument: TYPE_DOCUMENT | undefined;
  setNameModal: React.Dispatch<React.SetStateAction<ModalType>>;
};
function FaceVerify({ dataHash, setIsShowModal, faceSDK, typeDocument, setNameModal }: faceVerifyProps) {
  // variables check
  let STABLE_FACE_NUM = 2;
  let validCount = 0;
  let isTakePicture = true;
  let animation: any = null;
  let stateAnimation = 'FAR';
  let b64ImageOvalFar = '';

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [streams, setStreams] = useState<MediaStream[]>([]);
  const [warningMessage, setWarningMessage] = useState<string>(
    'Di chuyển di động sao cho gương mặt nằm trong vòng tròn và thực hiện chuyển động theo hướng dẫn',
  );
  const { dataCapture, setDataCapture, dataConfig, setScreenShow } = useAppContext();
  const [loadingCam, setLoadingCam] = useState<boolean>(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const videoChunksRef = useRef<BlobPart[]>([]);
  const client_session = defineTransaction(VERSION);

  //get data context language
  const { dictionary } = useContext(LanguageContext);
  const messages = dictionary['face_verify'];
  const default_status = messages['default_status'];
  const error_status = messages['error_status'];
  const MESSAGE_NOTICE: any = {
    FIT: messages['face_steady'],
    TO_THE_RIGHT: default_status,
    TO_THE_LEFT: default_status,
    TO_THE_BOTTOM: default_status,
    TO_THE_TOP: default_status,
    NOT_STRAIGHT: default_status,
    TOO_FAR: messages['closer'],
    TOO_NEAR: messages['furthur'],
    INVALID: default_status,
    NO_FACE: default_status,
    TIME_EXCEED: error_status,
    'Library not loaded': error_status,
    'Image format error': default_status,
  };

  const handleStartRecording = () => {
    if (!streams[0]) return;

    const options = { mimeType: 'video/webm; codecs=vp8' };
    const mediaRecorder = new MediaRecorder(streams[0], options);
    mediaRecorderRef.current = mediaRecorder;

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        videoChunksRef.current.push(event.data);
      }
    };

    mediaRecorder.start();
  };

  const handleStopRecording = (): Promise<Blob> => {
    return new Promise((resolve) => {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.onstop = () => {
          const videoBlob = new Blob(videoChunksRef.current, { type: 'video/webm' });
          videoChunksRef.current = []; // Clear chunks
          resolve(videoBlob);
        };
        mediaRecorderRef.current.stop();
      } else {
        resolve(new Blob());
      }
    });
  };

  async function process(FaceVNPTBrowserSDK: any) {
    try {
      if (isTakePicture) {
        let imgD = null;
        let windowWidth = window.screen.width < window.outerWidth ? window.screen.width : window.outerWidth;
        let response = null;
        let percent_temp = 0;
        if (windowWidth < 1024) {
          if (videoRef.current && canvasRef.current) {
            if (stateAnimation == 'FAR') {
              percent_temp = 0.6 * 1.5 * 0.75;
              imgD = await drawImageOVal(percent_temp, 0.75, videoRef.current, canvasRef.current);
            }
            if (stateAnimation == 'NEAR' && dataConfig.DOUBLE_LIVENESS) {
              percent_temp = 1;
              imgD = await drawImageOVal(percent_temp, 1, videoRef.current, canvasRef.current);
            }
          }
          response = await FaceVNPTBrowserSDK.processFace(imgD, 2);
        } else {
          if (videoRef.current && canvasRef.current) {
            if (stateAnimation == 'FAR') {
              imgD = await drawImageOVal(0.4, 0.75, videoRef.current, canvasRef.current);
            }
            if (stateAnimation == 'NEAR') {
              imgD = await drawImageOVal(0.5, 1, videoRef.current, canvasRef.current);
            }
          }
          response = await FaceVNPTBrowserSDK.processFace(imgD, 1);
        }
        setWarningMessage(MESSAGE_NOTICE[response]);
        if (response === MESSAGE_FACE.FIT) {
          validCount++;
        } else {
          if (response === MESSAGE_FACE.TIME_EXCEED) {
            stopInteval();
            isTakePicture = false;
            FaceVNPTBrowserSDK.free();
            if (streams) {
              stopBothVideoAndAudio(streams);
            }
            if (dataConfig.HAS_RESULT_SCREEN) {
              setScreenShow('VALIDATIONS_RESULTS');
            }
          } else {
            validCount = 0;
          }
        }
      }

      let dataLivenessImg: DataLivenessImg = {
        near_img: '',
        far_img: '',
        scan3d: '',
      };
      // check liveness far
      if (validCount > STABLE_FACE_NUM && stateAnimation === 'FAR' && dataConfig.DOUBLE_LIVENESS) {
        // UI effects
        FaceVNPTBrowserSDK.notifyCapture();
        animation.playSegments([0, 170], true);
        animation.setSpeed(2);
        // take a pic from oval far
        if (videoRef.current && canvasRef.current) {
          const img_canvas_oval = (await drawImageOVal(1, 1, videoRef.current, canvasRef.current)) as HTMLCanvasElement;
          b64ImageOvalFar = getImageReduceSize(img_canvas_oval, dataConfig.MAX_SIZE_IMAGE);
        }

        // reset state and set NEAR to check livess next time
        validCount = 0;
        stateAnimation = 'NEAR';

        // take a break and reset message warning
        isTakePicture = false;
        // waiting to reset message warning
        setTimeout(() => {
          isTakePicture = true;
        }, PROCESSING_DELAY);
      }
      // end check liveness far

      // check liveness near and close
      let file3D = null;
      if (validCount > STABLE_FACE_NUM) {
        if (stateAnimation === 'NEAR' && dataConfig.DOUBLE_LIVENESS) {
          setLoadingCam(false);
          FaceVNPTBrowserSDK.notifyCapture();
          isTakePicture = false;
          animation.playSegments([170, 300], true);
          animation.setSpeed(1.5);
          // get 3d image
          const maks3D = FaceVNPTBrowserSDK.getResult();
          file3D = new Blob([maks3D], { type: 'text' });
        } else {
          animation.playSegments([35, 120], true);
          animation.setSpeed(1.5);
          FaceVNPTBrowserSDK.free();
          stopInteval();
          isTakePicture = false;
          FaceVNPTBrowserSDK.notifyCapture();
          // get 3d image
          const maks3D = FaceVNPTBrowserSDK.getResult();
          file3D = new Blob([maks3D], { type: 'text' });
        }
        const res3dImg = await uploadImage(file3D, dataConfig);
        if (res3dImg.error) {
          throw new Error(res3dImg.error);
        }
        dataLivenessImg.scan3d = res3dImg.object.hash;

        if (videoRef.current && canvasRef.current) {
          const img_canvas_oval = (await drawImageOVal(1, 1, videoRef.current, canvasRef.current)) as HTMLCanvasElement;
          videoRef.current.src = '';
          const b64ImageOvalNear = getImageReduceSize(img_canvas_oval, dataConfig.MAX_SIZE_IMAGE);
          const nearImgRes = await uploadImage(b64toBlob(b64ImageOvalNear), dataConfig);
          if (nearImgRes.error) {
            throw new Error(nearImgRes.error);
          }
          if (dataConfig.DOUBLE_LIVENESS) {
            const farImgRes = await uploadImage(b64toBlob(b64ImageOvalFar), dataConfig);
            if (farImgRes.error) {
              throw new Error(farImgRes.error);
            }
            dataLivenessImg.far_img = farImgRes.object.hash;
          } else {
            dataLivenessImg.far_img = nearImgRes.object.hash;
          }
          const imgHashNearImg = nearImgRes.object.hash;
          dataLivenessImg.near_img = imgHashNearImg;

          if (imgHashNearImg) {
            const compareParams = {
              hashFront: dataHash.hashFront,
              hashFace: imgHashNearImg,
            };

            try {
              const videoBlob = await handleStopRecording(); // Stop recording and get video
              const [livenessFaceRes, compareFaceRes, maskedFaceRes] = await Promise.allSettled([
                dataConfig.CHECK_LIVENESS_FACE
                  ? liveness3DWeb(dataLivenessImg, dataConfig, client_session)
                  : Promise.resolve(false),
                dataConfig.COMPARE_FACE
                  ? compareFaceMobile(compareParams, dataConfig, client_session)
                  : Promise.resolve(false),
                dataConfig.CHECK_MASKED_FACE
                  ? maskedFaceMobile({ hashFace: imgHashNearImg }, dataConfig, client_session)
                  : Promise.resolve(false),
              ]);
              if (
                livenessFaceRes.status === 'fulfilled' &&
                compareFaceRes.status === 'fulfilled' &&
                maskedFaceRes.status === 'fulfilled'
              ) {
                const dataUpdated = {
                  ...dataCapture,
                  hash_img: imgHashNearImg,
                  base64_doc_img: {
                    img_front: dataCapture.base64_doc_img.img_front,
                    img_back: dataCapture.base64_doc_img.img_back,
                  },
                  base64_face_img: {
                    img_face_far: b64ImageOvalFar,
                    img_face_near: b64ImageOvalNear,
                  },
                  liveness_face: livenessFaceRes.value,
                  compare: compareFaceRes.value,
                  maskedFaceRes: maskedFaceRes.value,
                  videoRecorded: videoBlob,
                };

                setDataCapture(dataUpdated);
                setWarningMessage('Hoành thành');
                if (dataConfig.HAS_RESULT_SCREEN) {
                  setScreenShow('VALIDATIONS_RESULTS');
                } else {
                  onShowResult(dataConfig, dataUpdated);
                }
              }
            } catch (error) {
              console.log('Error:', error);
            } finally {
              stopBothVideoAndAudio(streams);
            }
          }
        }
        if (dataConfig.DOUBLE_LIVENESS) {
          FaceVNPTBrowserSDK.free();
          stopInteval();
        } else {
          setLoadingCam(false);
          validCount = 0;
          isTakePicture = true;
        }
        stopBothVideoAndAudio(streams);
      }
    } catch (error) {
      console.log('🚀 ~ process ~ error:', error);
      setNameModal('result');
      stopBothVideoAndAudio(streams);
      setIsShowModal(true);
    }
  }

  const onCheckCamera = () => {
    async function onStartWebcam() {
      setLoadingCam(true);
      await handleTurnOnWebCam({
        videoRef: videoRef.current,
        streams: streams,
        turnOnFrontCam: true, // true for turn on front camera
        fakeCamLabel: dataConfig.FAKE_CAM_LABEL,
      });
      setLoadingCam(false);
      handleStartRecording();
    }
    onStartWebcam();
  };

  const onCheckSizeScreen = () => {
    // check size screen to get oval json
    const windowWidth = window.screen.width < window.outerWidth ? window.screen.width : window.outerWidth;
    let oval: any = dataConfig.URL_WEB_OVAL || '/lib/web-oval.json';
    if (windowWidth < 1024) {
      oval = dataConfig.URL_MOBILE_OVAL || '/lib/mobile-oval-1.json';
    }
    animation = bodymovin.loadAnimation({
      container: document.querySelector('#animation') as any,
      path: oval, // Required
      renderer: 'svg', // Required
      loop: false, // Optional
      autoplay: false, // Optional
      name: 'oval', // Name for future reference. Optional.
    });
  };

  // check faceSDK and init sdk if it hasnt init
  const isFaceFlow = dataConfig.FLOW_TAKEN === 'FACE';
  let faceSDK_2: any = null;
  const onCheckFaceSDK = () => {
    // check if only taken FACE flow then init new sdk
    if (!faceSDK && isFaceFlow) {
      const getSDK = async () => {
        const sdkLivenessFace = await (window as any).FaceVNPTBrowserSDK;
        sdkLivenessFace.init();
        faceSDK_2 = sdkLivenessFace;
      };
      getSDK();
    }
  };

  useEffect(() => {
    // onCheckCamera();
    // onCheckSizeScreen();
    // onCheckFaceSDK();
  }, []);

  let interval: any = null;
  const stopInteval = () => {
    clearInterval(interval);
  };
  useEffect(() => {
    interval = setInterval(() => {
      if (faceSDK && isTakePicture) {
        process(faceSDK);
      } else {
        if (faceSDK_2 && isTakePicture) {
          process(faceSDK_2);
        }
      }
    }, 350);
    return () => clearInterval(interval);
  }, [faceSDK]);

  return (
    <div className="vnpt-flex vnpt-flex-col vnpt-bg-transparent vnpt-justify-center vnpt-items-center vnpt-mx-auto vnpt-mt-8 ">
      <p className="vnpt-text-balance vnpt-text-sm vnpt-font-normal vnpt-font-inter vnpt-text-center vnpt-mb-4">
        Di chuyển di động sao cho gương mặt nằm trong vòng tròn và thực hiện chuyển động theo hướng dẫn
      </p>
      <canvas className="vnpt-hidden" ref={canvasRef} id="canvas">
        {' '}
      </canvas>
      <div className="vnpt-flex vnpt-flex-col vnpt-mx-auto vnpt-mt-6 vnpt-rounded-lg vnpt-justify-center vnpt-items-center">
        {!!warningMessage && !loadingCam ? (
          <div className="warning-message vnpt-w-full vnpt-py-1 vnpt-px-3 vnpt-z-20 sm:-vnpt-mb-4 -vnpt-mb-14">
            <div className="vnpt-text-sm vnpt-text-center vnpt-font-medium vnpt-font-inter vnpt-text-white">
              {warningMessage}
            </div>
          </div>
        ) : (
          <Loading />
        )}
        <div className="vnpt-relative vnpt-overflow-hidden vnpt-z-10">
          <div id="animation" />
          <video
            playsInline
            className="-vnpt-scale-x-100 vnpt-absolute vnpt-inset-0 -vnpt-z-10 sm:vnpt-p-1"
            id="video"
            ref={videoRef}
          />
        </div>
      </div>
    </div>
  );
}
export default FaceVerify;
