import React from 'react'

type Props = {}

export default function Retake({ }: Props) {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M17.0925 4.375H14.4088L14.0963 3.4375C13.9456 2.98229 13.6552 2.58621 13.2664 2.30566C12.8775 2.02512 12.4101 1.87441 11.9306 1.875H8.06938C7.59002 1.87464 7.12276 2.02544 6.73407 2.30597C6.34538 2.58649 6.05504 2.98245 5.90437 3.4375L5.59125 4.375H2.9075C2.30235 4.37566 1.72217 4.61635 1.29426 5.04426C0.866351 5.47217 0.625662 6.05235 0.625 6.6575V15.845C0.626323 16.4497 0.867304 17.0293 1.29514 17.4566C1.72298 17.884 2.30278 18.1243 2.9075 18.125H17.095C17.6997 18.1237 18.2793 17.8827 18.7066 17.4549C19.134 17.027 19.3743 16.4472 19.375 15.8425V6.655C19.3737 6.05028 19.1327 5.47074 18.7049 5.04338C18.277 4.61601 17.6972 4.37566 17.0925 4.375ZM18.125 15.8425C18.1247 16.1162 18.0158 16.3787 17.8222 16.5722C17.6287 16.7658 17.3662 16.8747 17.0925 16.875H2.9075C2.63377 16.8747 2.37134 16.7658 2.17778 16.5722C1.98422 16.3787 1.87533 16.1162 1.875 15.8425V6.655C1.87599 6.3817 1.98517 6.11991 2.17866 5.92689C2.37215 5.73387 2.6342 5.62533 2.9075 5.625H6.04187C6.173 5.6249 6.30077 5.58357 6.4071 5.50685C6.51343 5.43012 6.59294 5.3219 6.63437 5.1975L7.09 3.83063C7.15842 3.62503 7.28986 3.44621 7.46566 3.31955C7.64146 3.19289 7.8527 3.12482 8.06938 3.125H11.9306C12.1474 3.12476 12.3587 3.19289 12.5346 3.31968C12.7104 3.44648 12.8418 3.62549 12.91 3.83125L13.3656 5.1975C13.4071 5.3219 13.4866 5.43012 13.5929 5.50685C13.6992 5.58357 13.827 5.6249 13.9581 5.625H17.0925C17.3662 5.62533 17.6287 5.73422 17.8222 5.92778C18.0158 6.12134 18.1247 6.38377 18.125 6.6575V15.8425Z" fill="#111127" stroke="#111127" stroke-width="0.7" />
            <path d="M10.2028 7C8.65097 7 7.31808 7.98763 6.73005 9.3956L6.3387 9.2328C6.24596 9.1941 6.13999 9.21736 6.07039 9.29151C6.00061 9.36586 5.98059 9.47623 6.01989 9.57135L6.64245 11.0815C6.66834 11.1438 6.71664 11.1934 6.77761 11.2186C6.8384 11.2441 6.90634 11.2433 6.9664 11.2163L8.42123 10.57C8.51287 10.5294 8.57182 10.4353 8.57072 10.3316C8.56943 10.2281 8.50828 10.1354 8.41572 10.0969L7.92245 9.8918C8.31949 8.97431 9.19052 8.33438 10.2026 8.33438C11.5876 8.33438 12.7145 9.52999 12.7145 10.9999C12.7145 12.4698 11.5873 13.6656 10.2026 13.6656C9.58756 13.6656 9.02487 13.4292 8.58853 13.0377C8.33382 12.8093 7.95459 12.8202 7.7175 13.0684C7.71438 13.0718 7.71144 13.0747 7.70832 13.0781C7.447 13.3511 7.46591 13.796 7.74634 14.0473C8.40874 14.6409 9.26637 15 10.2026 15C12.2963 15 14 13.2058 14 10.9999C14.0004 8.79436 12.2967 7 10.2028 7Z" fill="#111127" stroke="#111127" stroke-width="0.5" />
        </svg>
    )
}
