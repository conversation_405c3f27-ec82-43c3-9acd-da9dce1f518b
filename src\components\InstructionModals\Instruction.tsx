import React, { Dispatch, SetStateAction, useContext } from 'react';
import { LIST_DOCUMENT_ID } from '../../constants';
import { LanguageContext } from '../../context/LanguageProvider';
import { useAppContext } from '../../hooks';
import { ModalType } from '../../types';
import { changeThemeDefault } from '../../utils';
import Close from '../Icons/Close';
import Step1_QRscan from '../Icons/cccd-chip/step1_QRscan';
import Step2_QRscan from '../Icons/cccd-chip/step2_QRscan';
import Step3_QRscan from '../Icons/cccd-chip/step3_QRscan';
import StepCCCD1 from '../Icons/cccd/stepCCCD1';
import StepCCCD2 from '../Icons/cccd/stepCCCD2';
import WarningCCCD1 from '../Icons/cccd/warningCCCD1';
import WarningCCCD2 from '../Icons/cccd/warningCCCD2';
import WarningCCCD3 from '../Icons/cccd/warningCCCD3';
import FrontLicense from '../Icons/license/frontLicense';
import WarningLicense1 from '../Icons/license/warningLicense1';
import WarningLicense2 from '../Icons/license/warningLicense2';
import WarningLicense3 from '../Icons/license/warningLicense3';
import FrontPassport from '../Icons/passport/frontPassport';
import WarningPassport1 from '../Icons/passport/warningPassport1';
import WarningPassport2 from '../Icons/passport/warningPassport2';
import WarningPassport3 from '../Icons/passport/warningPassport3';
import CloseFailed from '../Icons/CloseFailed';
import { Modal } from 'antd';

type Props = {
  isShowModal: boolean;
  setIsShowModal: Dispatch<SetStateAction<boolean>>;
  nameModal: ModalType;
  selectedDocumentID: number | null;
  resetAction: () => void;
};

export default function Intruction({ setIsShowModal, nameModal, isShowModal, selectedDocumentID, resetAction }: Props) {
  const { dataConfig } = useAppContext();
  //get data context language
  const { dictionary, userLanguage } = useContext(LanguageContext);
  const modal = dictionary['modal'];
  interface ModalProps {
    title: string;
    instroImgs: any[];
    warningImgs: any[];
  }

  const defaultModal: ModalProps = {
    title: modal['title_cccd'],
    instroImgs: [<StepCCCD1 des={modal['step_1']} />, <StepCCCD2 des={modal['step_2']} />],
    warningImgs: [
      <WarningCCCD1 des={modal['warning_blur']} />,
      <WarningCCCD2 des={modal['warning_corner']} />,
      <WarningCCCD3 des={modal['warning_flare']} />,
    ],
  };
  let modalContent: ModalProps = defaultModal;

  switch (selectedDocumentID) {
    case LIST_DOCUMENT_ID.CITIZEN_CARD_ID_OLD:
      modalContent = {
        title: modal['title_cccd'],
        instroImgs: [<StepCCCD1 des={modal['step_1']} />, <StepCCCD2 des={modal['step_2']} />],
        warningImgs: [
          <WarningCCCD1 des={modal['warning_blur']} />,
          <WarningCCCD2 des={modal['warning_corner']} />,
          <WarningCCCD3 des={modal['warning_flare']} />,
        ],
      };
      break;
    case LIST_DOCUMENT_ID.PASSPORT_ID:
      modalContent = {
        title: modal['title_passport'],
        instroImgs: [<FrontPassport />],
        warningImgs: [
          <WarningPassport1 des={modal['warning_blur']} />,
          <WarningPassport2 des={modal['warning_corner']} />,
          <WarningPassport3 des={modal['warning_flare']} />,
        ],
      };
      break;
    case LIST_DOCUMENT_ID.DRIVE_LICENSE_ID:
      modalContent = {
        title: modal['title_license'],
        instroImgs: [<FrontLicense />],
        warningImgs: [
          <WarningLicense1 des={modal['warning_blur']} />,
          <WarningLicense2 des={modal['warning_corner']} />,
          <WarningLicense3 des={modal['warning_flare']} />,
        ],
      };
      break;
    case LIST_DOCUMENT_ID.AUTO_DETECT_DOCUMENT:
      modalContent = {
        title: modal['title_other_paper'],
        instroImgs: [<StepCCCD1 des={modal['step_1']} />, <StepCCCD2 des={modal['step_2']} />],
        warningImgs: [
          <WarningCCCD1 des={modal['warning_blur']} />,
          <WarningCCCD2 des={modal['warning_corner']} />,
          <WarningCCCD3 des={modal['warning_flare']} />,
        ],
      };
      break;
    case LIST_DOCUMENT_ID.CITIZEN_CARD_ID_CHIP:
      if (dataConfig.HAS_QR_SCAN) {
        modalContent = {
          title: modal['title_ccgc'],
          instroImgs: [
            <Step1_QRscan des={modal['qr_step_1']} />,
            <Step2_QRscan des={modal['qr_step_2']} />,
            <Step3_QRscan des={modal['qr_step_3']} />,
          ],
          warningImgs: [
            <WarningCCCD1 des={modal['warning_blur']} />,
            <WarningCCCD2 des={modal['warning_corner']} />,
            <WarningCCCD3 des={modal['warning_flare']} />,
          ],
        };
      } else {
        modalContent = {
          title: modal['title_cccd'],
          instroImgs: [<StepCCCD1 des={modal['step_1']} />, <StepCCCD2 des={modal['step_2']} />],
          warningImgs: [
            <WarningCCCD1 des={modal['warning_blur']} />,
            <WarningCCCD2 des={modal['warning_corner']} />,
            <WarningCCCD3 des={modal['warning_flare']} />,
          ],
        };
      }
      break;
    default:
      modalContent = defaultModal;
      break;
  }

  const ContentTakePhoto = () => (
    <div className="vnpt-w-full sm:vnpt-mt-4">
      <div className="vnpt-border-t vnpt-border-gray-400 sm:vnpt-border-b vnpt-py-4">
        <div className="vnpt-flex vnpt-justify-center vnpt-items-center">
          {modalContent.instroImgs.map((img) => (
            <div className="vnpt-flex vnpt-justify-center vnpt-items-center vnpt-flex-1 vnpt-w-14 sm:vnpt-w-auto">
              {img}
            </div>
          ))}
        </div>
        <div className="vnpt-mt-4">
          <div className="vnpt-flex vnpt-items-baseline">
            <span className="vnpt-flex-shrink-0 vnpt-mr-2 vnpt-bg-gray-900 vnpt-inline-block vnpt-rounded-full vnpt-h-2 vnpt-w-2" />
            <p className="vnpt-text-sm vnpt-font-open vnpt-font-normal vnpt-text-gray-900">{modal['guide_1']}</p>
          </div>
          <div className="vnpt-flex vnpt-items-baseline">
            <span className="vnpt-flex-shrink-0 vnpt-mr-2 vnpt-bg-gray-900 vnpt-inline-block vnpt-rounded-full vnpt-h-2 vnpt-w-2" />
            <p className="vnpt-text-sm vnpt-font-open vnpt-font-normal vnpt-text-gray-900">{modal['guide_2']}</p>
          </div>
        </div>
      </div>
      <div className="vnpt-mt-4">
        <div className="vnpt-flex vnpt-justify-center vnpt-items-start">
          {modalContent.warningImgs.map((img) => (
            <div className="vnpt-flex vnpt-justify-center vnpt-items-center vnpt-flex-1 vnpt-w-14 sm:vnpt-w-auto">
              {img}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const contentFaceVerify = () => (
    <div className="vnpt-w-full vnpt-max-w vnpt-border-t vnpt-border-t-gray-400 vnpt-pt-2">
      <div>
        <span>Vui lòng xem hướng dẫn để trải nghiệm dễ dàng hơn:</span>
      </div>
      <div className="vnpt-mt-2 vnpt-rounded-md vnpt-max-w-[430px] vnpt-mx-auto vnpt-overflow-hidden">
        <video controls playsInline autoPlay width={'100%'}>
          <source
            src={
              userLanguage === 'vi'
                ? dataConfig.URL_VIETNAMESE_VIDEO_TUTORIAL || '/lib/scan-face.mp4'
                : dataConfig.URL_ENGLISH_VIDEO_TUTORIAL || '/lib/english-tutorial.mp4'
            }
            type="video/mp4"
          />
        </video>
      </div>
      <div className="vnpt-mt-2">
        <div className="vnpt-flex vnpt-items-baseline">
          <span className="vnpt-flex-shrink-0 vnpt-mr-2 vnpt-bg-primary vnpt-inline-block vnpt-rounded-full vnpt-h-2 vnpt-w-2" />
          <span className="vnpt-text-sm">{modal['video_guide_2']}</span>
        </div>
        <div className="vnpt-flex vnpt-items-baseline">
          <span className="vnpt-flex-shrink-0 vnpt-mr-2 vnpt-bg-primary vnpt-inline-block vnpt-rounded-full vnpt-h-2 vnpt-w-2" />
          <span className="vnpt-text-sm">{modal['video_guide_3']}</span>
        </div>
      </div>
    </div>
  );

  const contentResultFaceVerify = () => (
    <Modal
      zIndex={9999999999999}
      footer={null}
      closable={false}
      open={isShowModal}
      onOk={() => setIsShowModal(false)}
      onCancel={() => setIsShowModal(false)}
      destroyOnClose
    >
      <div className="vnpt-flex vnpt-flex-col vnpt-items-center vnpt-p-4 vnpt-mx-auto">
        <div className="vnpt-hidden">
          <style dangerouslySetInnerHTML={changeThemeDefault(dataConfig)} />
        </div>
        <div className="vnpt-w-full vnpt-max-w vnpt-border-t vnpt-border-t-gray-400 vnpt-pt-2">
          <div className="vnpt-flex vnpt-justify-center vnpt-items-center">
            <CloseFailed />
          </div>
          <div>
            <div className="vnpt-flex vnpt-flex-col vnpt-items-center vnpt-gap-4">
              <span className="vnpt-font-bold vnpt-text-md vnpt-font-inter vnpt-mt-2">Thông báo</span>
              <p className="vnpt-font-normal vnpt-text-base vnpt-m-0 vnpt-font-inter vnpt-text-center">
                Hệ thống đang bận. <br />
                Quý khách vui lòng thử lại!
              </p>
            </div>
          </div>
          <div className="vnpt-flex vnpt-gap-4 vnpt-mt-4 vnpt-justify-center">
            <div
              onClick={() => {
                setIsShowModal(false);
                resetAction();
              }}
              className="sm:vnpt-w-52 vnpt-text-center vnpt-py-2 vnpt-px-8 vnpt-border-2 vnpt-border-solid vnpt-border-primary vnpt-text-base vnpt-font-medium vnpt-font-inter vnpt-bg-white vnpt-rounded-full vnpt-text-primary vnpt-cursor-pointer"
            >
              Bỏ qua
            </div>
            <div
              onClick={() => {
                setIsShowModal(false);
                resetAction();
              }}
              className="sm:vnpt-w-52 vnpt-text-center vnpt-py-2 vnpt-px-8 vnpt-bg-primary vnpt-rounded-full vnpt-text-base vnpt-font-medium vnpt-font-inter vnpt-text-white vnpt-cursor-pointer"
            >
              Thử lại
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );

  if (nameModal === 'result') {
    return contentResultFaceVerify();
  }

  return (
    <Modal
      open={isShowModal}
      closeIcon={<Close />}
      onOk={() => setIsShowModal(false)}
      footer={null}
      onCancel={() => setIsShowModal(false)}
    >
      <div className="vnpt-flex vnpt-flex-col vnpt-items-center vnpt-bg-white vnpt-p-4 vnpt-mx-auto">
        <div className="vnpt-hidden">
          <style dangerouslySetInnerHTML={changeThemeDefault(dataConfig)} />
        </div>
        <div className="vnpt-flex vnpt-pb-2 vnpt-w-full">
          <div className="vnpt-text-center vnpt-w-full vnpt-px-1">
            <p className="vnpt-text-md vnpt-m-0 sm:vnpt-text-md vnpt-font-semibold vnpt-px-6">
              {' '}
              {nameModal === 'verify' ? modal['title_video'] : modalContent.title}{' '}
            </p>
          </div>
        </div>
        {nameModal === 'instruction' && ContentTakePhoto()}
        {nameModal === 'verify' && contentFaceVerify()}
        <div className="vnpt-mt-4 vnpt-w-full">
          <div
            onClick={() => setIsShowModal(false)}
            className="vnpt-cursor-pointer vnpt-py-2 vnpt-text-center vnpt-text-base vnpt-font-semibold vnpt-w-1/2 vnpt-mx-auto vnpt-font-open vnpt-text-white vnpt-bg-primary vnpt-rounded-primary"
          >
            Tôi đã hiểu
          </div>
        </div>
      </div>
    </Modal>
  );
}
