/** Text styles */
const fontSize = {
  /** Heading */
  h0: ['6rem', '7rem'],
  h1: ['3.5rem', '4.5rem'],
  h2: ['3rem', '3.5rem'],
  h3: ['2.5rem', '3rem'],
  h4: ['2rem', '2.5rem'],
  h5: ['1.5rem', '2rem'],

  'h-d': ['6rem', '7rem'],
  'h-xl': ['3.5rem', '4.5rem'],
  'h-lg': ['3rem', '3.5rem'],
  'h-md': ['2.5rem', '3rem'],
  'h-sm': ['2rem', '2.5rem'],
  'h-xs': ['1.5rem', '2rem'],
  'h-xxs': ['1.25rem', '1.5rem'],

  /** Subtitle */
  's-xl': ['3rem', '3.5rem'],
  's-l': ['2.5rem', '3rem'],
  's-md': ['2rem', '2.5rem'],
  's-sm': ['1.5rem', '2rem'],

  /** body */
  xs: ['0.75rem', '1rem'],
  sm: ['0.875rem', '1.25rem'],
  base: ['1rem', '1.5rem'],
  md: ['1.125rem', '1.75rem'],
  lg: ['1.25rem', '1.75rem'],
};
module.exports = {
  corePlugins: {
    preflight: false, // ignore default reset CSS by tailwind
  },
  important: true,
  prefix: 'vnpt-',
  content: [
    // Example content paths...
    './public/**/*.html',
    './src/**/*.{js,jsx,ts,tsx,vue}',
  ],
  theme: {
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
    },
    fontSize,
    fontFamily: {
      robo: ['Roboto', 'sans-serif'],
      open: ['Open Sans', 'sans-serif'],
      inter: ['Inter', 'sans-serif'],
    },
    extend: {
      colors: {
        hero: 'var(--vnpt-background-sdk)',
        primary: 'var(--vnpt-primary-color)',
        line: 'rgba(225, 225, 225, 0.15)',
        'empty-cam': 'rgba(23, 24, 28, 0.7)',
        success: '#24A148',
        'blur-success': 'rgba(0, 189, 8, 0.1)',
        warning: '#122F41',
        'result-bg': 'rgba(0, 189, 8, 0.10)',
        gray: {
          0: 'var(--vnpt-text-color-default)',
          300: '#F5F5F5',
          400: '#CBCBCB',
          500: '#AFAFAF',
          600: '#757575',
          700: 'rgba(18, 18, 18, 1)',
          900: '#111127',
        },
      },
      spacing: {
        96: '24rem',
        128: '32rem',
      },
      borderRadius: {
        primary: '10px',
      },
    },
  },
  variants: {
    extend: {},
  },
  plugins: [
    require('tailwindcss')('./tailwindcss-config.js'),
    'prettier-plugin-tailwindcss',
  ],
  debug: true, // Enable debug mode
};
