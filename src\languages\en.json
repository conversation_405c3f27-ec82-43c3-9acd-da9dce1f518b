{"instruction": "Instruction", "step": "Step", "take_photo": "TAKE PHOTO", "retake": "RETAKE", "upload_photo": "UPLOAD IMAGE", "take_front_side": "THE FRONT PHOTO", "take_back_side": "THE BACK PHOTO", "next": "NEXT", "guide_take_photo": "Please place the documents to fit the rectangular frame, taking photos with enough light and clarity", "warning_take_1": "Select ‘Retake’ if the photo is blurry, glaring or unclear", "warning_take_2": "Select 'Next' to go to the next step", "face_authentication": "Face authentication", "QR_guide": "Please insert the QR code into the center of the camera", "noti": "Notice", "QR_invalid": "Invalid QR code!", "QR_error_mes": "Please scan the QR code on the ID card", "agree": "AGREE", "home": {"title": "DOCUMENT AUTHENTICATION", "subTitle": "Select the document type you want to authenticate", "cmt": "ID card, Citizen ID card", "passpost": "Passport", "cmt_army": "Military ID card", "license": "Driver's license", "cmt_chip": "Citizen ID card with a chip", "other_papers": "Other papers", "start": "START"}, "face_verify": {"default_status": "Make sure the face fits in the frame", "error_status": "An error occurred during liveness", "face_steady": "Hold steady", "closer": "Move closer", "furthur": "Move further"}, "modal": {"title_cccd": "Instructions for taking ID card/Citizen ID card", "title_ccgc": "Instructions for taking Citizen ID card with a chip", "title_passport": "Instructions for taking Passport", "title_license": "Instructions for taking Driver's license", "title_other_paper": "Instructions for taking Document", "title_video": "Instructions for authenticating the face of the document", "guide_1": "Bring the document close to the camera so that the 4 corners of the document coincide with the limited area", "guide_2": "Capture clear and complete information on documents", "video_guide_1": "Watch the video for an easier experience", "video_guide_2": "Make the face fit in the small frame and the large frame", "video_guide_3": "Do not wear glasses, masks, or take photos in backlight", "start": "START", "got_it": "I GOT IT", "step_1": "Step 1: Take a front side", "step_2": "Step 2: Take a back side", "qr_step_1": "Step 1: Scan QR code", "qr_step_2": "Step 2: Take a front side", "qr_step_3": "Step 3: Take a back side", "warning_blur": "Don't shoot too blurry", "warning_corner": "Don't take pictures of corners", "warning_flare": "No flare shots"}, "result": {"title": "AUTHENTIC RESULTS", "information": "Personal information", "validation": "Validation", "Qr": "QR Code", "re_action": "TRY AGAIN", "copy": "Copy logs", "informationFields": {"document": "Documents", "id_number": "ID card number", "full_name": "Full name", "date_of_birth": "Date of birth", "permanent_residence": "Registered permanent address", "gender": "Gender", "provide_date": "Issue date", "exprire_date": "Expiration date", "provide_location": "Issue place", "compare": "Comparison", "result": "Result"}, "validationFields": {"document_type": "Document type", "font_back_document": "The front and back of the document", "card_blur": "Blurred/Absence of corners", "quality_id": "ID quality", "quality_provide_date": "Issued date", "expire_warning": "Expiry date", "document_verification": "Document identification", "infor_warning": "Warnings", "override_image": "Overlay image", "eyes_open": "Open eyes", "face_blur": "Blurred face", "authenticate_face": "Face validation", "masked_face": "Covered face"}, "QR": {"title": "QR Information", "des": "Compare information on the photo documents", "des_1": "ID card number", "des_2": "Full name", "des_3": "Date of birth"}}, "response": {"valid": "<PERSON><PERSON>", "invalid": "Invalid", "expired": "Expired", "unexpired": "Unexpired", "good": "Good", "blurred": "Blurred", "different": "Different", "identical": "Identical", "no_live_shooting": "No live shooting", "live_shooting": "Live shooting", "yes": "Yes", "no": "No", "face_matching": "Face matching", "faces_mismatch": "Faces do not match", "mat_truoc_mat_sau_khong_khop": "Documents have mismatched front and back sides", "chat_luong_anh_dau_vao_khong_dat_chuan": "Input image quality is not up to standard", "anh_dau_vao_mo_nhoe": "Documents are blurred/smudged", "anh_dau_vao_mat_goc": "Warning the content of the input document image is blurred/lost corners", "id_mo_nhoe": "Warning ID information is blurred", "ho_ten_mo_nhoe": "Warning name information is blurred", "ngay_cap_mo_nhoe": "Warning issue date information is blurred", "ngay_het_han_mo_nhoe": "Warning expiration date information is blurred", "ngay_sinh_mo_nhoe": "Warning expiration date information is blurred", "id_duc_lo": "Warning perforated ID", "anh_mat_truoc_bi_che": "Warning hidden document information", "id_xac_suat_thap": "Warning low probability ID information", "dia_chi_thuong_tru_khong_khop_noi_cap": "Warning permanent address mismatch the place of issue", "qua_han_mat_truoc": "Warning expired document", "qua_han_mat_sau": "Warning expired document", "id_sua_xoa": "ID number has been edited or deleted", "id_ko_hop_le": "Invalid ID number", "id_dob_ko_khop": "Year of birth code does not match", "id_gender_ko_khop": "Gender codes do not matchp", "id_post_code_ko_khop": "Province/City code does not match", "invalid_dob": "Invalid date of birth code", "den_trang_ko_hop_le": "Photocopy document", "fake_printing": "Invalid document", "id_invalid_length": "Invalid ID number"}}