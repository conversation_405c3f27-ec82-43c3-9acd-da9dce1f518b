import React, { useState, useEffect, useRef } from 'react';

const CameraSelector: React.FC = () => {
    const [videoSources, setVideoSources] = useState<MediaDeviceInfo[]>([]);
    const [selectedVideoSource, setSelectedVideoSource] = useState<string | undefined>(undefined);
    const videoRef = useRef<HTMLVideoElement | null>(null);
    const streamRef = useRef<MediaStream | null>(null);

    const gotDevices = (deviceInfos: any) => {
        const values = [selectedVideoSource];
        const options: JSX.Element[] = [];

        deviceInfos.forEach((deviceInfo: any) => {
            if (deviceInfo.kind === 'videoinput') {
                options.push(
                    <option key={deviceInfo.deviceId} value={deviceInfo.deviceId}>
                        {deviceInfo.label || `camera ${options.length + 1}`}
                    </option>
                );
            }
        });

        setVideoSources(options as any);

        if (Array.prototype.slice.call(options).some((n) => n.props.value === values[0])) {
            setSelectedVideoSource(values[0]);
        }
    };

    const gotStream = (stream: MediaStream) => {
        if (videoRef.current) {
            videoRef.current.srcObject = stream;
            videoRef.current.play().catch(console.error);
        }

        streamRef.current = stream; // Store the stream in the ref

        return navigator.mediaDevices.enumerateDevices().then(gotDevices).catch(handleError);
    };

    const handleError = (error: any) => {
        console.error('navigator.MediaDevices.getUserMedia error:', error.message, error.name);
    };

    const start = () => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach((track) => {
                track.stop();
            });
        }

        const constraints: MediaStreamConstraints = {
            video: { deviceId: selectedVideoSource ? { exact: selectedVideoSource } : undefined },
        };

        navigator.mediaDevices
            .getUserMedia(constraints)
            .then(gotStream)
            .then(gotDevices)
            .catch(handleError);
    };

    const handleChangeVideoSource = (event: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedVideoSource(event.target.value);
    };

    useEffect(() => {
        navigator.mediaDevices.enumerateDevices().then(gotDevices).catch(handleError);
    }, []);

    useEffect(() => {
        start();
    }, [selectedVideoSource]);

    return (
        <div>
            <video id="video" playsInline autoPlay ref={videoRef}></video>

            <div className="select">
                <label htmlFor="videoSource">Video source: </label>
                <select id="videoSource" onChange={handleChangeVideoSource} value={selectedVideoSource || ''}>
                    {videoSources as any}
                </select>
            </div>

        </div>
    );
};

export default CameraSelector;
