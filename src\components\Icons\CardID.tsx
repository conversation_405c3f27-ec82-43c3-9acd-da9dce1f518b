import React from 'react'

type Props = {}

export default function CardID({ }: Props) {
    return (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_7950_42771)">
                <path d="M28.3333 28H3.66675C1.64404 28 0 26.356 0 24.3333V7.66675C0 5.64404 1.64404 4 3.66675 4H28.3333C30.356 4 32 5.64404 32 7.66675V24.3333C32 26.356 30.356 28 28.3333 28ZM3.66675 6C2.74805 6 2 6.74805 2 7.66675V24.3333C2 25.252 2.74805 26 3.66675 26H28.3333C29.252 26 30 25.252 30 24.3333V7.66675C30 6.74805 29.252 6 28.3333 6H3.66675Z" fill="white" />
                <path d="M10.0002 15.9998C8.16284 15.9998 6.66699 14.5037 6.66699 12.6665C6.66699 10.8291 8.16284 9.33301 10.0002 9.33301C11.8376 9.33301 13.3335 10.8291 13.3335 12.6665C13.3335 14.5037 11.8376 15.9998 10.0002 15.9998ZM10.0002 11.333C9.26562 11.333 8.66699 11.9316 8.66699 12.6665C8.66699 13.4011 9.26562 13.9998 10.0002 13.9998C10.7349 13.9998 11.3335 13.4011 11.3335 12.6665C11.3335 11.9316 10.7349 11.333 10.0002 11.333Z" fill="white" />
                <path d="M15 22.6665C14.448 22.6665 14 22.2185 14 21.6665V20.9998C14 20.0811 13.252 19.333 12.3333 19.333H7.66675C6.74805 19.333 6 20.0811 6 20.9998V21.6665C6 22.2185 5.552 22.6665 5 22.6665C4.448 22.6665 4 22.2185 4 21.6665V20.9998C4 18.9771 5.64404 17.333 7.66675 17.333H12.3333C14.356 17.333 16 18.9771 16 20.9998V21.6665C16 22.2185 15.552 22.6665 15 22.6665Z" fill="white" />
                <path d="M27.0002 12H19.667C19.115 12 18.667 11.552 18.667 11C18.667 10.448 19.115 10 19.667 10H27.0002C27.5522 10 28.0002 10.448 28.0002 11C28.0002 11.552 27.5522 12 27.0002 12Z" fill="white" />
                <path d="M27.0002 17.333H19.667C19.115 17.333 18.667 16.885 18.667 16.333C18.667 15.781 19.115 15.333 19.667 15.333H27.0002C27.5522 15.333 28.0002 15.781 28.0002 16.333C28.0002 16.885 27.5522 17.333 27.0002 17.333Z" fill="white" />
                <path d="M27.0002 22.667H19.667C19.115 22.667 18.667 22.219 18.667 21.667C18.667 21.115 19.115 20.667 19.667 20.667H27.0002C27.5522 20.667 28.0002 21.115 28.0002 21.667C28.0002 22.219 27.5522 22.667 27.0002 22.667Z" fill="white" />
            </g>
            <defs>
                <clipPath id="clip0_7950_42771">
                    <rect width="32" height="32" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}
