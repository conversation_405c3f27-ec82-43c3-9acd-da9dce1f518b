import { OrcType, dataConfigType } from "../types";

const setHeaders = (token: string, token_id: string, token_key: string, type_header?: string, HEADERS_REQUEST?: Record<string, string>) => {
    const objHeader: any = {
        // "Authorization": "Bearer " + token,
        // "Token-id": token_id,
        // "Token-key": token_key,
        // "mac-address": "WEB-001"
    };
    if (type_header) {
        objHeader["Content-Type"] = type_header;
    }
    if (HEADERS_REQUEST) {
        Object.assign(objHeader, HEADERS_REQUEST);
    }
    return objHeader;
}
export async function uploadImage(blobImg: Blob, configData: dataConfigType) {
    let url = configData.BACKEND_URL + `${configData.ENDPOINT_UPLOAD_IMAGE}?challengeCode=${configData.CHALLENGE_CODE}`;
    var formData = new FormData();
    formData.append("file", blobImg);
    formData.append("title", "upload_file");
    formData.append("description", "ic_upload_file");
    const token = configData.ACCESS_TOKEN;
    const token_id = configData.TOKEN_ID;
    const token_key = configData.TOKEN_KEY;

    const requestOptions = {
        method: 'POST',
        headers: setHeaders(token, token_id, token_key, undefined, configData.HEADERS_REQUEST),
        body: formData,
    };
    const response = fetch(url, requestOptions).then(res => res.json())
        .then(res => res)
        .catch(error => error)
    return response;
}


export async function livenessCardMobile(hash: string, configData: dataConfigType, clientSection: string) {
    let url = configData.BACKEND_URL + `${configData.ENDPOINT_LIVENESS_CARD}?challengeCode=${configData.CHALLENGE_CODE}`;
    const jsonData = {
        "img": hash,
        "client_session": clientSection,
        "token": "e41-1b6d-45c9-9",
        "crop_param": "0,0",
    };
    const token = configData.ACCESS_TOKEN;
    const token_id = configData.TOKEN_ID;
    const token_key = configData.TOKEN_KEY;
    const type_header = 'application/json'
    const response = await fetch(url, {
        method: "POST",
        mode: "cors",
        credentials: "same-origin",
        headers: setHeaders(token, token_id, token_key, type_header, configData.HEADERS_REQUEST),
        body: JSON.stringify(jsonData)
    }).then(res => res.json())
        .then(res => res)
        .catch(error => error)
    return response;
}


export async function ocrOneSideMobile(data: any, configData: dataConfigType, clientSection: string) {
    var url = configData.BACKEND_URL + `${configData.ENDPOINT_OCR_DOCUMENT_FRONT}?challengeCode=${configData.CHALLENGE_CODE}`;
    var jsonData = {
        "img_front": data.hash_front,
        "client_session": clientSection,
        "token": "e41-1b6d-45c9-9",
        "type": data.typeDocument,
        "crop_param": "0,0",
        "validate_postcode": true,
    };
    var token = configData.ACCESS_TOKEN;
    var token_id = configData.TOKEN_ID;
    var token_key = configData.TOKEN_KEY;
    const type_header = 'application/json'

    const response = await fetch(url, {
        method: "POST",
        mode: "cors",
        credentials: "same-origin",
        headers: setHeaders(token, token_id, token_key, type_header, configData.HEADERS_REQUEST),
        body: JSON.stringify(jsonData)
    }).then(res => res.json())
        .then(res => res)
        .catch(error => error)
    return response;
}


export async function ocrFullSideMobile(data: OrcType, configData: dataConfigType, clientSection: string) {
    var url = configData.BACKEND_URL + `${configData.ENDPOINT_OCR_DOCUMENT}?challengeCode=${configData.CHALLENGE_CODE}`;
    var jsonData = {
        "img_front": data.hash_front,
        "img_back": data.hash_back,
        "client_session": clientSection,
        "token": "e41-1b6d-45c9-9",
        "type": data.typeDocument,
        "crop_param": "0,0",
        "validate_postcode": true,
    }
    var token = configData.ACCESS_TOKEN;
    var token_id = configData.TOKEN_ID;
    var token_key = configData.TOKEN_KEY;
    const type_header = 'application/json';

    const response = await fetch(url, {
        method: "POST",
        mode: "cors",
        credentials: "same-origin",
        headers: setHeaders(token, token_id, token_key, type_header, configData.HEADERS_REQUEST),
        body: JSON.stringify(jsonData)
    }).then(res => res.json())
        .then(res => res)
        .catch(error => error)
    return response;
}

export async function liveness3DWeb(dataLivenessImg: DataLivenessImg, configData: dataConfigType, clientSection: string) {
    let url = configData.BACKEND_URL + `${configData.ENDPOINT_LIVENESS_FACE}?challengeCode=${configData.CHALLENGE_CODE}`;
    var jsonData = {
        "client_session": clientSection,
        "token": "e41-1b6d-45c9-9",
        "far_img": dataLivenessImg.far_img,
        "near_img": dataLivenessImg.near_img,
        "scan3d": dataLivenessImg.scan3d,
    };
    const token = configData.ACCESS_TOKEN;
    const token_id = configData.TOKEN_ID;
    const token_key = configData.TOKEN_KEY;
    const type_header = 'application/json'

    const response = await fetch(url, {
        method: "POST",
        mode: "cors",
        credentials: "same-origin",
        headers: setHeaders(token, token_id, token_key, type_header, configData.HEADERS_REQUEST),
        body: JSON.stringify(jsonData)
    }).then(res => res.json())
        .then(res => res)
        .catch(error => error)
    return response;
}

export async function compareFaceMobile(dataHash: { hashFront: string, hashFace: string }, configData: dataConfigType, clientSection: string) {
    let url = configData.BACKEND_URL + `${configData.ENDPOINT_COMPARE_FACE}?challengeCode=${configData.CHALLENGE_CODE}`;
    var jsonData = {
        "img_front": dataHash.hashFront,
        "img_face": dataHash.hashFace,
        "client_session": clientSection,
        "token": "e41-1b6d-45c9-9",
    };
    var token = configData.ACCESS_TOKEN;
    var token_id = configData.TOKEN_ID;
    var token_key = configData.TOKEN_KEY;
    const type_header = 'application/json'
    const response = await fetch(url, {
        method: "POST",
        mode: "cors",
        credentials: "same-origin",
        headers: setHeaders(token, token_id, token_key, type_header, configData.HEADERS_REQUEST),
        body: JSON.stringify(jsonData)
    }).then(res => res.json())
        .then(res => res)
        .catch(error => error)
    return response;
}


export async function maskedFaceMobile(data: any, configData: dataConfigType, clientSection: string) {
    let url = configData.BACKEND_URL + `${configData.ENDPOINT_MASKED_FACE}?challengeCode=${configData.CHALLENGE_CODE}`;
    var jsonData = {
        "img": data.hashFace,
        "face_bbox": null,
        "face_lmark": null,
        "client_session": clientSection,
        "token": "e41-1b6d-45c9-9",
    };
    var token = configData.ACCESS_TOKEN;
    var token_id = configData.TOKEN_ID;
    var token_key = configData.TOKEN_KEY;
    const type_header = 'application/json'

    const response = await fetch(url, {
        method: "POST",
        mode: "cors",
        credentials: "same-origin",
        headers: setHeaders(token, token_id, token_key, type_header, configData.HEADERS_REQUEST),
        body: JSON.stringify(jsonData)
    }).then(res => res.json())
        .then(res => res)
        .catch(error => error)
    return response;
}

// end verify face apis


export type DataLivenessImg = {
    near_img: string,
    far_img: string,
    scan3d: string
}
