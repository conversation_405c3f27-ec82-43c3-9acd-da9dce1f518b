import React, { useContext } from 'react'
import { useAppContext } from '../hooks';
import { LanguageContext } from '../context/LanguageProvider';

type Props = {
    numberTotalStep: number,
    numberStep: number
}

export default function StepChecking({ numberTotalStep, numberStep }: Props) {
    const step = new Array(numberStep).fill("vnpt");
    const stepRemaining = new Array(numberTotalStep - numberStep).fill("vnpt");
    //get data context language
    const { dictionary } = useContext(LanguageContext);

    const { dataConfig } = useAppContext();
    if (!dataConfig.SHOW_STEP) return null;
    return (
        <div className='vnpt-flex vnpt-items-center vnpt-justify-center vnpt-mt-2'>
            <p className='vnpt-text-sm vnpt-font-open vnpt-font-semibold vnpt-text-gray-0'>{dictionary['step']} {numberStep}/{numberTotalStep}</p>
            {step.map((item, index) => <div key={item + index} className='vnpt-ml-2 vnpt-h-1 vnpt-w-12 vnpt-bg-primary'></div>)}
            {stepRemaining.map((item, index) => <div key={item + index} className='vnpt-ml-2 vnpt-h-1 vnpt-w-12 vnpt-bg-gray-500'></div>)}
        </div>
    )
}
