<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>eKYC SDK</title>
    <script src="web-sdk-version-*******.js"></script>
    <script src="./lib/VNPTQRBrowserApp.js"></script>
    <script src="./lib/VNPTBrowserSDKAppV4.0.0.js"></script>
  </head>

  <body>
    <div id="ekyc_sdk_intergrated"></div>
    <script>
      // customer's configg
      const CALL_BACK_END_FLOW = async (result) => {
        console.log('result ==>', result);
      };

      const BACKEND_URL = 'https://sandbox-idg.vnpt.vn';
      const TOKEN_ID = '2e01d212-de90-3336-e063-1f08a30a6689';
      const TOKEN_KEY =
        'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANU6keywZGalbsYw7JZQGxD9suHH+SShjlQhhiCb/32Qzh8Qte8L/5A5jDk6buFd5AFTcQoajVKFarESzylX5cUCAwEAAQ==';
      const ACCESS_TOKEN =
        'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RDR5rU0Ka7FmkVoSwO-Vpxgbg-R7buPjJNjPCTwn0TcI1lhP_P3tEIMO2ubAJ4QWPHUneUaTMqFHFEejxtW7P3wfFRTeFKcXjUXFohdsVOECwSs-yOwhxBRjLiilh82T9P_BRJyN6BU6c_BfZf7Svqr3VtofXqTnsTtNzn4kuqctCVpw7uruay6upDnUiQYWUxwvAe56gDA3h3qhzglUiJkZqcnhLqSyreTe3tcDYGU-pxgEq0NtdSZMKYhJFztEqHf-7qLfNKVaNbV4_uXQE3iazIbU_8vl-wxz3piYjFpNtwNkB9OvXyQjjpZwK8US647HsgvOw7Kd8fTgSpx_ug';

      const LIST_TYPE_DOCUMENT = [-1, 4, 5, 6, 7];
      const USE_METHOD = 'BOTH';
      const CUSTOM_THEME = {
        PRIMARY_COLOR: '#184693',
        TEXT_COLOR_DEFAULT: '#142730',
        BACKGROUND_COLOR: '#ffffff',
      };
      const FLOW_TAKEN = 'BOTH';
      const HAS_RESULT_SCREEN = true;

      // api calls
      const DOUBLE_LIVENESS = true;
      const CHECK_LIVENESS_CARD = true;
      const OCR_DOCUMENT = true;
      const COMPARE_FACE = true;
      const CHECK_MASKED_FACE = true;
      const CHECK_LIVENESS_FACE = true;
      const DOCUMENT_TYPE_START = -1;
      const MAX_SIZE_IMAGE = 0.1; // MB

      // config endpoint starts with "/"
      const ENDPOINT_UPLOAD_IMAGE = '';
      const ENDPOINT_LIVENESS_CARD = '';
      const ENDPOINT_LIVENESS_FACE = '';
      const ENDPOINT_MASKED_FACE = '';
      const ENDPOINT_COMPARE_FACE = '';
      const ENDPOINT_OCR_DOCUMENT = '';
      const ENDPOINT_OCR_DOCUMENT_FRONT = '';
      const TIME_EXCEED_FACE_LIVENESS = 20; // seconds

      const HEADERS_REQUEST = {
        'key-1': 'vnpt-ekyc-sdk-01',
        'key-2': 'vnpt-ekyc-sdk-02',
      };

      const dataConfig = {
        BACKEND_URL,
        TOKEN_KEY,
        TOKEN_ID,
        ACCESS_TOKEN,
        CALL_BACK_END_FLOW,
        LIST_TYPE_DOCUMENT,
        CUSTOM_THEME,
        USE_METHOD,
        FLOW_TAKEN,
        HAS_RESULT_SCREEN,
        CHECK_LIVENESS_CARD,
        OCR_DOCUMENT,
        DOUBLE_LIVENESS,
        COMPARE_FACE,
        CHECK_MASKED_FACE,
        CHECK_LIVENESS_FACE,
        DOCUMENT_TYPE_START,
        HEADERS_REQUEST,
        MAX_SIZE_IMAGE,
        TIME_EXCEED_FACE_LIVENESS,
      };

      window.addEventListener('DOMContentLoaded', function () {
        if (window.SDK) {
          window.SDK.launch(dataConfig);
        }
      });
    </script>
  </body>
</html>
