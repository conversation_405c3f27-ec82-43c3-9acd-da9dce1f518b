import React, { useEffect, useState } from 'react';
import { initZxing } from '../../utils';
import ButtonV2 from '../Icons/ButtonV2';
import Camera from '../Icons/Camera';
import Upload from '../Icons/Upload';
import StepChecking from '../StepChecking';
import { useAppContext } from '../../hooks';


type Props = {
    setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>
}
const QRCodeUpload = ({ setIsShowModal }: Props) => {
    const { setDataCapture, dataCapture, setScreenShow } = useAppContext()

    const [qrCodeContent, setQRCodeContent] = useState<string>('');
    const [imgPreview, setImgPreview] = useState<string>('');
    const [sdkQR, setSdkQR] = useState<any>();

    useEffect(() => {
        const getSDK = async () => {
            const sdk = await initZxing();
            setSdkQR(sdk)
        }
        getSDK();
    }, [])


    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            try {
                const reader = new FileReader();

                reader.onload = function (e) {
                    if (!e.target) return;
                    setImgPreview(e.target.result as string);
                };
                reader.readAsDataURL(file);
                const arrayBuffer = await readFileAsArrayBuffer(file as any);
                const codeReader = new sdkQR.BrowserQRCodeReader();
                const img = new Image();
                img.src = URL.createObjectURL(file);
                img.onload = async () => {
                    const imageResizes = resizeImage(img, 480, 640);
                    setImgPreview(imageResizes);

                    const img2 = new Image();
                    img2.src = imageResizes;

                    const result = await codeReader.decodeFromImageElement(img2);
                    setQRCodeContent(result.text);
                    setDataCapture({ ...dataCapture, QRscan: result.text || "no data" })
                    console.log("🚀 ~ file: index.tsx:54 ~ img.onload= ~ result.text:", result.text)
                    URL.revokeObjectURL(img.src);
                };
            } catch (error) {
                console.error("Error reading QR code:", error);
                setQRCodeContent('Không tìm thấy QR code trong ảnh.')
            }
        } else {
            setImgPreview("");
        }
        setScreenShow('PRERARE_CAPTURE')
    };

    function resizeImage(image: any, width: any, height: any) {
        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        const context = canvas.getContext("2d");
        if (context)
            context.drawImage(image, 0, 0, width, height);
        return canvas.toDataURL("image/jpeg");
    }

    function readFileAsArrayBuffer(file: React.ChangeEvent<HTMLInputElement>) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
                if (event.target)
                    resolve(event.target.result);
            };
            reader.onerror = (error) => {
                reject(error);
            };
            reader.readAsArrayBuffer(file as any);
        });
    }

    return (
        <>
            {/* <input type="file" id="fileInput" capture="environment" accept="image/*" onChange={handleFileChange} />
            <img id="imgPreview" width="300" src={imgPreview} alt="img_preview" />
            <div id="result">QR Code content: {qrCodeContent}</div> */}


            <div className='vnpt-mx-auto vnpt-max-w-sm vnpt-mt-8'>
                <div>
                    <h2 className='vnpt-text-primary vnpt-uppercase vnpt-font-open vnpt-text-h-xs vnpt-text-center'>QR CODE</h2>
                </div>
                <StepChecking numberStep={1} numberTotalStep={5} />
                <div className='vnpt-mx-auto vnpt-mt-6 sm:vnpt-w-96 vnpt-h-60 vnpt-bg-empty-cam vnpt-rounded-lg vnpt-flex vnpt-flex-col vnpt-justify-center vnpt-items-center vnpt-mb-8'>
                    <ButtonV2 action={() => { setScreenShow('QR_SCAN') }} className='vnpt-mb-8 vnpt-w-44 sm:vnpt-w-52' label='SCAN QR' icon={<Camera />} />
                    <div className='vnpt-relative vnpt-overflow-hidden vnpt-inline'>
                        <input type="file" id="fileInput" capture="environment" accept="image/*" onChange={handleFileChange} className='vnpt-absolute vnpt-inset-0 vnpt-opacity-0 vnpt-cursor-pointer hover:vnpt-bg-primary' />
                        <ButtonV2 label='CHỤP MÃ QR' className='vnpt-w-44 sm:vnpt-w-52' icon={<Upload />} />
                    </div>
                </div>
                <div className='vnpt-mt-20 vnpt-text-center' onClick={() => setIsShowModal(true)}>
                    <p className='vnpt-text-primary vnpt-font-bold vnpt-font-open vnpt-text-lg vnpt-underline vnpt-inline-block vnpt-cursor-pointer'>Hướng dẫn</p>
                </div>
            </div>
        </>




    );
};

export default QRCodeUpload;
