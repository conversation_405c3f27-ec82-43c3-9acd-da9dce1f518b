import React from 'react'

type Props = {}

export default function Camera({ }: Props) {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_7950_30136)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.41724 1.5C7.16018 1.5 6.02767 2.19503 5.46474 3.31396L4.88028 4.47564H3.30128C1.48223 4.47564 0 5.94645 0 7.75814V15.1406C0 16.9523 1.48223 18.4231 3.30128 18.4231H16.6987C18.5178 18.4231 20 16.9523 20 15.1406V7.75814C20 5.94641 18.5178 4.47564 16.6987 4.47564H15.1197L14.5352 3.31396C13.9723 2.19503 12.8398 1.5 11.5827 1.5H8.41724ZM7.28463 4.22107C7.50035 3.79226 7.93317 3.52583 8.41728 3.52583H11.5828C12.0669 3.52583 12.4997 3.79227 12.7154 4.22107L13.5813 5.94213C13.7541 6.28552 14.1065 6.5015 14.4912 6.5015H16.6987C17.3981 6.5015 17.9647 7.06695 17.9647 7.75814V15.1406C17.9647 15.8318 17.3981 16.3972 16.6987 16.3972H3.30128C2.60192 16.3972 2.03526 15.8318 2.03526 15.1406V7.75814C2.03526 7.06695 2.60192 6.5015 3.30128 6.5015H5.50881C5.89347 6.5015 6.24594 6.2855 6.41874 5.94213L7.28463 4.22107ZM10 6.69479C7.78918 6.69479 5.98824 8.48236 5.98824 10.6834C5.98824 12.8844 7.78918 14.672 10 14.672C12.2108 14.672 14.0118 12.8844 14.0118 10.6834C14.0118 8.48232 12.2108 6.69479 10 6.69479ZM8.02349 10.6834C8.02349 9.60282 8.90888 8.72062 10 8.72062C11.0911 8.72062 11.9765 9.60282 11.9765 10.6834C11.9765 11.7639 11.0911 12.6461 10 12.6461C8.90888 12.6461 8.02349 11.7639 8.02349 10.6834Z" fill="#111127" />
            </g>
            <defs>
                <clipPath id="clip0_7950_30136">
                    <rect width="20" height="20" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}
