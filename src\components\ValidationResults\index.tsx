import React, { useContext, useEffect, useState } from 'react';
import { LIST_DOCUMENT_ID } from '../../constants';
import { LanguageContext } from '../../context/LanguageProvider';
import { useAppContext, useCopyToClipboard } from '../../hooks';
import { TYPE_DOCUMENT } from '../../types';
import { onShowResult } from '../../utils';
import StepChecking from '../StepChecking';
import Information from './Information';
import QRCode from './QRCode';
import Validation from './Validation';

type Props = {
  resetAction: () => void;
  typeDocument: TYPE_DOCUMENT | undefined;
};
type Tabs = 'information' | 'validation' | 'qrCode';
export default function ValidationResults({ resetAction, typeDocument }: Props) {
  const [tab, setTab] = useState<Tabs>('information');
  const { dataCapture, dataConfig } = useAppContext();
  //get data context language
  const { dictionary } = useContext(LanguageContext);
  const result = dictionary['result'];

  const { copy, copied } = useCopyToClipboard();
  const handleCopyClick = () => {
    const dataLog = {
      type_document: dataCapture.type_document,
      liveness_card_front: dataCapture.liveness_card_front,
      liveness_card_back: dataCapture.liveness_card_back,
      ocr: dataCapture.orc,
      liveness_face: dataCapture.liveness_face,
      masked: dataCapture.maskedFaceRes,
      hash_img: dataCapture.hash_img,
      compare: dataCapture.compare,
      base64_doc_img: {
        // img_front: dataCapture.base64_doc_img.img_front,
        // img_back: dataCapture.base64_doc_img.img_back,
      },
      base64_face_img: {
        // img_face_far: dataCapture.base64_face_img.img_face_far,
        // img_face_near: dataCapture.base64_face_img.img_face_near,
      },
      data_hash_document: dataCapture.data_hash_document,
      qrCode: dataCapture.QRscan,
    };
    copy(JSON.stringify(dataLog, null, 2));
  };

  useEffect(() => {
    onShowResult(dataConfig, dataCapture);
  }, []);

  // check is new cccd
  const hasQR = dataCapture.type_document === LIST_DOCUMENT_ID.CITIZEN_CARD_ID_CHIP;

  const AlertCopied = (
    <div
      className={`vnpt-flex vnpt-items-center vnpt-fixed vnpt-transition-all vnpt-duration-500 ${
        copied ? 'vnpt-left-3' : '-vnpt-left-full'
      } vnpt-bg-primary vnpt-text-gray-0 vnpt-rounded-lg vnpt-mx-auto vnpt-max-w-[200px] vnpt-text-sm vnpt-font-bold vnpt-px-4 vnpt-py-3`}
      role="alert"
    >
      <svg
        className="vnpt-fill-current vnpt-w-4 vnpt-h-4 vnpt-mr-2"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
      >
        <path d="M12.432 0c1.34 0 2.01.912 2.01 1.957 0 1.305-1.164 2.512-2.679 2.512-1.269 0-2.009-.75-1.974-1.99C9.789 1.436 10.67 0 12.432 0zM8.309 20c-1.058 0-1.833-.652-1.093-3.524l1.214-5.092c.211-.814.246-1.141 0-1.141-.317 0-1.689.562-2.502 1.117l-.528-.88c2.572-2.186 5.531-3.467 6.801-3.467 1.057 0 1.233 1.273.705 3.23l-1.391 5.352c-.246.945-.141 1.271.106 1.271.317 0 1.357-.392 2.379-1.207l.6.814C12.098 19.02 9.365 20 8.309 20z" />
      </svg>
      <p>Copied to clipboard!</p>
    </div>
  );

  const LeftSideImages = (
    <div className="sm:vnpt-bg-gray-300 vnpt-bg-gray-0 vnpt-p-4 vnpt-flex vnpt-items-center vnpt-justify-center vnpt-flex-col vnpt-gap-4">
      {dataCapture.base64_doc_img.img_front && (
        <div className="vnpt-rounded-xl vnpt-overflow-hidden vnpt-w-auto lg:vnpt-w-56">
          <img src={dataCapture.base64_doc_img.img_front} alt="img-front" />
        </div>
      )}

      {dataCapture.base64_doc_img.img_back && (
        <div className="vnpt-rounded-xl vnpt-overflow-hidden vnpt-w-auto lg:vnpt-w-56">
          <img src={dataCapture.base64_doc_img.img_back} alt="img-back" />
        </div>
      )}

      {dataCapture.base64_doc_img.far_img && (
        <div className="vnpt-rounded-xl vnpt-overflow-hidden vnpt-w-auto lg:vnpt-w-56">
          <img src={dataCapture.base64_doc_img.far_img} alt="img-far" />
        </div>
      )}

      {dataCapture.base64_doc_img.near_img && (
        <div className="vnpt-rounded-xl vnpt-overflow-hidden vnpt-w-auto lg:vnpt-w-56">
          <img src={dataCapture.base64_doc_img.near_img} alt="img-far" />
        </div>
      )}
    </div>
  );

  const RightSideContent = (
    <div className="vnpt-flex-1">
      <div className="vnpt-flex sm:vnpt-px-4 vnpt-flex-wrap">
        {dataConfig.SHOW_TAB_RESULT_INFORMATION && (
          <div
            onClick={() => setTab('information')}
            style={{ borderBottomStyle: 'solid' }}
            className={`sm:vnpt-px-6 sm:vnpt-py-3 vnpt-px-3 vnpt-py-1 vnpt-text-center vnpt-flex-1 vnpt-cursor-pointer ${
              tab === 'information'
                ? 'vnpt-border-b-4 vnpt-border-hero vnpt-text-hero'
                : 'vnpt-text-gray-500  vnpt-border-b vnpt-border-b-gray-400'
            }`}
          >
            <p className="vnpt-font-open vnpt-text-base vnpt-whitespace-nowrap vnpt-font-semibold">
              {result['information']}
            </p>
          </div>
        )}

        {dataConfig.SHOW_TAB_RESULT_VALIDATION && (
          <div
            onClick={() => setTab('validation')}
            style={{ borderBottomStyle: 'solid' }}
            className={`sm:vnpt-px-6 sm:vnpt-py-3 vnpt-px-3 vnpt-py-1 vnpt-text-center vnpt-flex-1 vnpt-cursor-pointer ${
              tab === 'validation'
                ? 'vnpt-border-b-4 vnpt-border-hero vnpt-text-hero'
                : 'vnpt-text-gray-500  vnpt-border-b vnpt-border-b-gray-400'
            }`}
          >
            <p className="vnpt-font-open vnpt-text-base vnpt-whitespace-nowrap vnpt-font-semibold">
              {result['validation']}
            </p>
          </div>
        )}

        {dataConfig.SHOW_TAB_RESULT_QRCODE && dataCapture.QRscan && (
          <div
            onClick={() => setTab('qrCode')}
            style={{ borderBottomStyle: 'solid' }}
            className={`sm:vnpt-px-6 sm:vnpt-py-3 vnpt-px-3 vnpt-py-1 vnpt-text-center vnpt-flex-1 vnpt-cursor-pointer ${
              tab === 'qrCode'
                ? 'vnpt-border-b-4 vnpt-border-hero vnpt-text-hero'
                : 'vnpt-text-gray-500  vnpt-border-b vnpt-border-b-gray-400'
            }`}
          >
            <p className="vnpt-font-open vnpt-text-base vnpt-whitespace-nowrap vnpt-font-semibold">{result['Qr']}</p>
          </div>
        )}
      </div>
      <div className="vnpt-mt-2 vnpt-px-4">
        {dataConfig.SHOW_TAB_RESULT_INFORMATION && tab === 'information' && <Information />}
        {dataConfig.SHOW_TAB_RESULT_VALIDATION && tab === 'validation' && <Validation />}
        {dataConfig.SHOW_TAB_RESULT_QRCODE && tab === 'qrCode' && <QRCode />}
      </div>
    </div>
  );

  const images = [];
  dataCapture.base64_doc_img.img_front && images.push(dataCapture.base64_doc_img.img_front);
  dataCapture.base64_doc_img.img_back && images.push(dataCapture.base64_doc_img.img_back);
  dataCapture.base64_face_img.img_face_far && images.push(dataCapture.base64_face_img.img_face_far);
  dataCapture.base64_face_img.img_face_near && images.push(dataCapture.base64_face_img.img_face_near);

  return (
    <>
      {AlertCopied}
      <div className="vnpt-grid vnpt-grid-cols-2 vnpt-gap-4 vnpt-mt-4 vnpt-px-4">
        {images.map((image, index) => (
          <div key={index} className="vnpt-relative">
            <img src={image} alt="image" className="vnpt-w-full vnpt-h-full vnpt-object-cover" />
          </div>
        ))}

        {/* {dataCapture.videoRecorded && (
          <div className="vnpt-mt-4 vnpt-flex vnpt-flex-col vnpt-items-center">
            <video controls className="vnpt-w-full sm:vnpt-w-3/4 lg:vnpt-w-1/2">
              <source src={URL.createObjectURL(dataCapture.videoRecorded)} type="video/webm" />
              Your browser does not support the video tag.
            </video>
            <a
              href={URL.createObjectURL(dataCapture.videoRecorded)}
              download="recorded_video.mp4"
              className="vnpt-mt-2 vnpt-bg-primary vnpt-text-white vnpt-py-2 vnpt-px-4 vnpt-rounded-full"
            >
              Tải video
            </a>
          </div>
        )} */}
      </div>

      <div className="vnpt-text-center vnpt-mt-4">
        <div
          onClick={resetAction}
          className="vnpt-inline-block vnpt-py-4 vnpt-px-20 vnpt-font-semibold vnpt-text-base vnpt-cursor-pointer vnpt-bg-primary vnpt-font-open vnpt-text-white vnpt-mt-2 vnpt-rounded-primary"
        >
          {result['re_action']}
        </div>
        <div
          onClick={handleCopyClick}
          className="vnpt-inline-block vnpt-ml-4 vnpt-py-4 vnpt-px-20 vnpt-font-semibold vnpt-text-base vnpt-cursor-pointer vnpt-bg-primary vnpt-font-open vnpt-text-white vnpt-mt-4 vnpt-rounded-primary"
        >
          Copy kết quả
        </div>
      </div>
    </>
  );
}
