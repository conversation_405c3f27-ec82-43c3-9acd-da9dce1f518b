import React, { useContext, useEffect, useRef, useState } from 'react';
import { LanguageContext } from '../context/LanguageProvider';
import { useAppContext, useUploadImage } from '../hooks';
import { captureProps } from '../types';
import { clearPhoto, getImageReduceSize, handleTurnOnWebCam, stopBothVideoAndAudio } from '../utils';
import ButtonCamera from './Icons/ButtonCamera';
import Loading from './Loading';

function CaptureOneSide({ onNextResult, setIsShowModal }: captureProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [streams, setStreams] = useState<MediaStream[]>([]);
  const { dataCapture, setDataCapture, dataConfig } = useAppContext();
  const { handleFileChange, previewURL, setPreviewURL } = useUploadImage();
  //get data context language
  const { dictionary } = useContext(LanguageContext);

  let imageFront = dataCapture.base64_doc_img.img_front;

  function handleNextResult() {
    onNextResult();
  }

  function takePiture() {
    setDataCapture({ ...dataCapture, base64_doc_img: { img_front: '' } });
    if (!canvasRef.current) return;
    if (!videoRef.current) return;
    const context: CanvasRenderingContext2D | null = canvasRef.current.getContext('2d');
    if (videoRef.current.videoWidth && context) {
      canvasRef.current.width = videoRef.current.videoWidth;
      canvasRef.current.height = videoRef.current.videoHeight;
      context.drawImage(videoRef.current, 0, 0, videoRef.current.videoWidth, videoRef.current.videoHeight);

      let resultb64: string = getImageReduceSize(canvasRef.current, dataConfig.MAX_SIZE_IMAGE);
      setDataCapture({
        ...dataCapture,
        base64_doc_img: { img_front: resultb64 },
      });
    } else {
      setDataCapture({
        ...dataCapture,
        base64_doc_img: { img_front: clearPhoto(canvasRef.current) },
      });
    }
    stopBothVideoAndAudio(streams);
  }

  function handleUploadImage(event: React.ChangeEvent<HTMLInputElement>) {
    setPreviewURL('');
    handleFileChange(event);
  }

  useEffect(() => {
    async function onStartWebcam() {
      setIsLoading(true);
      await handleTurnOnWebCam({
        videoRef: videoRef.current,
        streams: streams,
        turnOnFrontCam: false, // true for turn on front camera
        fakeCamLabel: dataConfig.FAKE_CAM_LABEL,
      });
      setIsLoading(false);
    }
    onStartWebcam();
  }, [dataCapture.base64_doc_img.img_front]);

  useEffect(() => {
    if (previewURL) {
      setDataCapture({
        ...dataCapture,
        base64_doc_img: { img_front: previewURL },
      });
      stopBothVideoAndAudio(streams);
    }
  }, [previewURL]);

  const buttonsControl = () => {
    let src: string | undefined = imageFront;

    return (
      <div>
        {src ? (
          <div className="vnpt-flex vnpt-gap-4">
            <div
              onClick={takePiture}
              className="sm:vnpt-w-52 vnpt-text-center vnpt-py-2 vnpt-px-8 vnpt-border-2 vnpt-border-solid vnpt-border-primary vnpt-text-base vnpt-font-medium vnpt-font-inter vnpt-bg-white vnpt-rounded-full vnpt-text-primary vnpt-cursor-pointer"
            >
              Chụp lại
            </div>
            <div
              onClick={src ? handleNextResult : undefined}
              className="sm:vnpt-w-52 vnpt-text-center vnpt-py-2 vnpt-px-8 vnpt-bg-primary vnpt-rounded-full vnpt-text-base vnpt-font-medium vnpt-font-inter vnpt-text-white vnpt-cursor-pointer"
            >
              Tiếp theo
            </div>
          </div>
        ) : (
          <div className="vnpt-cursor-pointer" onClick={takePiture}>
            <ButtonCamera />
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      <div className="  vnpt-flex vnpt-flex-col vnpt-justify-center vnpt-items-center vnpt-mx-auto vnpt-mt-8 vnpt-max-w-md">
        <p className="vnpt-font-inter vnpt-text-md vnpt-font-bold vnpt-text-center vnpt-mb-4">Chụp mặt trước giấy tờ</p>
        <p className="vnpt-text-balance vnpt-text-sm vnpt-font-normal vnpt-font-inter vnpt-text-center">
          Đặt mặt trước CCCD nằm giữa khung hình, chụp đủ ánh sáng, không lóa sáng và rõ nét.
        </p>
        <canvas className="vnpt-hidden" ref={canvasRef} id="canvas">
          {' '}
        </canvas>
        <div className="box vnpt-p-2 vnpt-mt-6">
          <div className="vnpt-mx-auto vnpt-h-60 sm:vnpt-h-auto sm:vnpt-w-96 vnpt-rounded-lg vnpt-min-w-80 vnpt-overflow-hidden vnpt-flex vnpt-justify-center vnpt-items-center">
            {isLoading && <Loading />}
            {!!imageFront ? (
              <div className="output">
                <img
                  className="vnpt-rounded-lg"
                  src={imageFront}
                  id="photo"
                  alt="The screen capture will appear in this box."
                />
              </div>
            ) : (
              <video className="vnpt-rounded-lg" playsInline ref={videoRef} id="video">
                Video stream not available!
              </video>
            )}
          </div>
        </div>

        {!isLoading && <div className="vnpt-mt-20">{buttonsControl()}</div>}
      </div>
    </div>
  );
}

export default CaptureOneSide;
