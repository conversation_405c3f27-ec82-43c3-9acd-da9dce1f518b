import React from 'react';

type Props = {};

export default function CloseFailed({}: Props) {
  return (
    <svg width="57" height="56" viewBox="0 0 57 56" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle opacity="0.2" cx="28.5" cy="28" r="28" fill="#CA2A2A" />
      <circle opacity="0.25" cx="28.5" cy="28" r="24" fill="#CA2A2A" />
      <circle cx="28.5" cy="28" r="20" fill="white" />
      <path
        d="M28.5 8C17.48 8 8.5 16.98 8.5 28C8.5 39.02 17.48 48 28.5 48C39.52 48 48.5 39.02 48.5 28C48.5 16.98 39.52 8 28.5 8ZM35.22 32.6C35.8 33.18 35.8 34.14 35.22 34.72C34.92 35.02 34.54 35.16 34.16 35.16C33.78 35.16 33.4 35.02 33.1 34.72L28.5 30.12L23.9 34.72C23.6 35.02 23.22 35.16 22.84 35.16C22.46 35.16 22.08 35.02 21.78 34.72C21.2 34.14 21.2 33.18 21.78 32.6L26.38 28L21.78 23.4C21.2 22.82 21.2 21.86 21.78 21.28C22.36 20.7 23.32 20.7 23.9 21.28L28.5 25.88L33.1 21.28C33.68 20.7 34.64 20.7 35.22 21.28C35.8 21.86 35.8 22.82 35.22 23.4L30.62 28L35.22 32.6Z"
        fill="#CA2A2A"
      />
    </svg>
  );
}
