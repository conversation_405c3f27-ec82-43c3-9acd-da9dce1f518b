import React from 'react';
import { DocumentProps } from '../types';
import ArrowRight from './Icons/ArrowRight';

type Props = {
  item: DocumentProps;
  action: (item: DocumentProps) => void;
};
const DocumentSelect: React.FC<any> = ({ item, action }: Props) => {
  return (
    <div
      onClick={() => action(item)}
      className="vnpt-w-full vnpt-flex vnpt-justify-between vnpt-pt-6 vnpt-cursor-pointer"
    >
      <div className="vnpt-w-16 vnpt-rounded-lg vnpt-bg-primary vnpt-flex vnpt-justify-center vnpt-items-center">
        {item.icon}
      </div>
      <div className="vnpt-w-full vnpt-flex vnpt-justify-between vnpt-items-center vnpt-ml-4 vnpt-py-4 vnpt-border-b vnpt-border-b-line vnpt-group">
        <p className="vnpt-text-gray-0 group-hover:vnpt-text-primary vnpt-font-medium vnpt-text-base">
          {item.name}
        </p>
        <div className="vnpt-mr-4">
          <ArrowRight className="vnpt-text-gray-0 group-hover:vnpt-text-primary" />
        </div>
      </div>
    </div>
  );
};

export default DocumentSelect;
