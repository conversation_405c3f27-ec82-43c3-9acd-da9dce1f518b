{"name": "ekyc-sdk-web-v2", "version": "*******", "description": "this is version upgrate for ekyc-web-sdk of capture", "main": "index.js", "scripts": {"lint": "eslint --ext .js,.jsx,.ts,.tsx .", "lint-fix": "eslint --fix --ext .js,.jsx,.ts,.tsx .", "start": "node dist/index.js", "build": "webpack --config webpack.config.js --progress", "test": "echo \"Error: no test specified\" && exit 1", "dev": "concurrently \"nodemon --watch src -e js,ts,jsx,tsx -x 'yarn run build'\" \"nodemon server.js --watch dist\""}, "repository": {"type": "git", "url": "git+ssh://**************/quanbh1911/ekyc-sdk-web-v2.git"}, "keywords": ["ekyc-web-sdk", "capture", "typescript"], "author": "quanbh", "license": "ISC", "bugs": {"url": "https://github.com/quanbh1911/ekyc-sdk-web-v2/issues"}, "homepage": "https://github.com/quanbh1911/ekyc-sdk-web-v2#readme", "devDependencies": {"@types/object-assign": "^4.0.30", "@types/uuid": "^9.0.1", "autoprefixer": "^10.4.14", "concurrently": "^9.0.1", "css-loader": "^6.7.3", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "nodemon": "^3.1.7", "postcss": "^8.4.23", "postcss-loader": "^7.3.0", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.70.0", "sass-loader": "^14.1.0", "style-loader": "^3.3.2", "tailwindcss": "^3.3.2", "typescript": "^5.0.4", "webpack-cli": "^5.1.1", "webpack-dev-server": "^5.1.0"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "antd": "^5.21.6", "axios": "^1.4.0", "browser-image-compression": "^2.0.2", "cors": "^2.8.5", "express": "^4.18.2", "lottie-web": "^5.12.2", "object-assign": "^4.1.1", "path": "^0.12.7", "ts-loader": "^9.4.2", "uuid": "^9.0.0", "webpack": "^5.83.1"}}