import React, { createContext, useState } from 'react';
import { AppContextData, SCREEN, dataConfigType, ResponseDataCapture } from '../types';

export const initResponseDataCapture: ResponseDataCapture = {
  hash_img: '',
  data_hash_document: { img_front: '', img_back: '' },
  base64_doc_img: {
    img_front: '',
    img_back: '',
    far_img: '',
    near_img: '',
  },
  base64_face_img: {
    img_face_far: '',
    img_face_near: '',
  },
  client_session: '',
  liveness_card_front: {
    dataBase64: '',
    object: {
      blur_face: '',
      face_swapping: null,
      face_swapping_prob: null,
      fake_liveness: null,
      fake_liveness_prob: null,
      fake_print_photo: null,
      fake_print_photo_prob: null,
      liveness: '',
      liveness_msg: '',
    },
  },
  liveness_card_back: {
    dataBase64: '',
    object: {
      blur_face: '',
      face_swapping: null,
      face_swapping_prob: null,
      fake_liveness: null,
      fake_liveness_prob: null,
      fake_print_photo: null,
      fake_print_photo_prob: null,
      liveness: '',
      liveness_msg: '',
    },
  },
  orc: {
    object: {
      back_type_id: null,
      warning_msg: '',
      tampering: {
        warning: [],
      },
      id_probs: '',
      warning: [],
      birth_day: '',
      birth_day_prob: null,
      card_type: '',
      checking_result_front: {
        check_photocopied_prob: null,
        check_photocopied_result: '',
        corner_cut_prob: [],
        corner_cut_result: '',
        edited_prob: null,
        edited_result: '',
        recaptured_prob: null,
        recaptured_result: '',
      },
      citizen_id: '',
      citizen_id_prob: null,
      corner_warning: '',
      expire_warning: '',
      gender: '',
      gender_prob: null,
      id: '',
      id_fake_prob: null,
      id_fake_warning: '',
      issue_date: '',
      issue_date_prob: null,
      issue_place: '',
      issue_place_prob: null,
      msg: '',
      name: '',
      name_prob: null,
      nation_policy: '',
      nation_slogan: '',
      nationality: '',
      quality_front: {
        blur_score: null,
        bright_spot_score: null,
      },
      rank: '',
      recent_location: '',
      recent_location_prob: null,
      type_id: null,
      valid_date: '',
      valid_date_prob: null,
      general_warning: [],
    },
  },
  compare: {
    object: {
      match_warning: '',
      msg: '',
      multiple_faces: '',
      prob: null,
      result: '',
    },
  },
  liveness_face: {
    object: {
      age: null,
      blur_face: '',
      blur_face_score: null,
      gender: '',
      is_eye_open: '',
      liveness: '',
      liveness_msg: '',
      liveness_prob: null,
    },
  },
  maskedFaceRes: {
    object: {
      masked: '',
    },
  },
  QRscan: null,
  othersData: {
    step: 'first',
  },
};

export const initDataConfig: dataConfigType = {
  // configs for API
  BACKEND_URL: '',
  TOKEN_KEY: '',
  TOKEN_ID: '',
  ACCESS_TOKEN: '',
  CALL_BACK_END_FLOW: async (result: any) => {},
  CALL_BACK_DOCUMENT_RESULT: async (document_result: any) => {},
  HEADERS_REQUEST: {},

  // config endpoint
  ENDPOINT_UPLOAD_IMAGE: '/file-service/v1/addFile',
  ENDPOINT_LIVENESS_CARD: '/ai/v1/web/card/liveness',
  ENDPOINT_LIVENESS_FACE: '/ai/v1/web/face/liveness-3d',
  ENDPOINT_MASKED_FACE: '/ai/v1/web/face/mask',
  ENDPOINT_COMPARE_FACE: '/ai/v1/web/face/compare',
  ENDPOINT_OCR_DOCUMENT: '/ai/v1/web/ocr/id',
  ENDPOINT_OCR_DOCUMENT_FRONT: '/ai/v1/web/ocr/id/front',

  // config api calls
  CHECK_LIVENESS_CARD: true,
  CHECK_LIVENESS_FACE: true,
  CHECK_MASKED_FACE: true,
  COMPARE_FACE: true,
  OCR_DOCUMENT: true,

  // configs for flow sdk
  FLOW_TAKEN: null,
  DOCUMENT_TYPE_START: 999,
  LIST_TYPE_DOCUMENT: [-1, 4, 5, 6, 7, 9],

  CHALLENGE_CODE: '00000',
  HAS_QR_SCAN: true,
  DOUBLE_LIVENESS: false,
  FAKE_CAM_LABEL: '',
  DEFAULT_LANGUAGE: 'vi',
  USE_METHOD: 'BOTH',
  URL_WEB_OVAL: '',
  URL_MOBILE_OVAL: '',
  URL_ENGLISH_VIDEO_TUTORIAL: '',
  URL_VIETNAMESE_VIDEO_TUTORIAL: '',
  MAX_SIZE_IMAGE: 1,
  TIME_EXCEED_FACE_LIVENESS: 600, // 10 minutes

  // configs for UI
  LINK_ICON_CCCD: '',
  LINK_ICON_PASSPORT: '',
  LINK_ICON_DRIVER_LICENSE: '',
  LINK_ICON_OTHER_PAPERS: '',
  LINK_ICON_QR_SCAN: '',

  // result UI
  HAS_RESULT_SCREEN: true,
  SHOW_TAB_RESULT_INFORMATION: true,
  SHOW_TAB_RESULT_VALIDATION: true,
  SHOW_TAB_RESULT_QRCODE: true,

  HAS_BACKGROUND_IMAGE: true,
  SHOW_STEP: true,
  CUSTOM_THEME: {
    PRIMARY_COLOR: '',
    TEXT_COLOR_DEFAULT: '',
    BACKGROUND_COLOR: '',
  },
};
// Create the context
export const AppContext = createContext<AppContextData | undefined>(undefined);

// Create a provider component to wrap your app with
export const AppContextProvider = ({ children }: any) => {
  // Define the data you want to pass through the context
  const [data, setData] = useState<ResponseDataCapture>(initResponseDataCapture);
  const [dataConfig, setDataConfig] = useState<dataConfigType>(initDataConfig);
  const [screenShow, setScreenShow] = useState<SCREEN>('LIST_DOCUMENT');

  const appData: AppContextData = {
    dataCapture: data,
    setDataCapture: (newData: ResponseDataCapture) => setData(newData),
    dataConfig: dataConfig,
    setDataConfig: (data: dataConfigType) => setDataConfig(data),
    setScreenShow: (value: SCREEN) => setScreenShow(value),
    screenShow: screenShow,
  };
  return <AppContext.Provider value={appData}>{children}</AppContext.Provider>;
};
