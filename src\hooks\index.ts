import { ChangeEvent, useContext, useEffect, useState } from "react";
import { AppContext } from "../context/AppProvider";
import { AppContextData } from "../types";


export const useUploadImage = () => {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [previewURL, setPreviewURL] = useState<string>('');

    const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
        if (!event.target.files?.[0]) return;
        const file: File | undefined = event.target.files?.[0];
        setSelectedFile(file);

        // Generate preview URL
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewURL(reader.result as string);
            };
            reader.readAsDataURL(file);
        } else {
            setPreviewURL('');
        }
    };

    const handleUpload = () => {
        if (selectedFile) {
            // Perform the upload logic here
            console.log('Uploading file:', selectedFile);
        }
    };

    return { handleFileChange, handleUpload, setPreviewURL, previewURL, selectedFile }
}


// Custom hook to consume the context
export const useAppContext = (): AppContextData => {
    const context: AppContextData | undefined = useContext(AppContext);
    if (!context) {
        throw new Error('useAppContext must be used within an AppContextProvider');
    }
    return context;
};



export const useCustomLocalStorage = (key: string, initialValue: string) => {
    // Get the stored value from local storage or use the initial value
    const [storedValue, setStoredValue] = useState(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            // If there's an error (e.g., local storage is disabled), return the initial value
            console.error('Error retrieving data from local storage:', error);
            return initialValue;
        }
    });

    // Update the stored value and local storage whenever the state value changes
    const setValue = (value: any) => {
        try {
            const valueToStore =
                value instanceof Function ? value(storedValue) : value;

            // Save to local storage
            window.localStorage.setItem(key, JSON.stringify(valueToStore));

            // Update the state
            setStoredValue(valueToStore);
        } catch (error) {
            // If there's an error (e.g., local storage is full or disabled), handle gracefully
            console.error('Error storing data in local storage:', error);
        }
    };

    // Return the stored value and the function to set a new value
    return [storedValue, setValue];
};


type CopyToClipboardResult = {
    copy: (text: string) => void;
    copied: boolean;
};
export const useCopyToClipboard = (): CopyToClipboardResult => {
    const [copied, setCopied] = useState(false);

    const copy = (text: string) => {
        try {
            navigator.clipboard.writeText(text);
            setCopied(true);
        } catch (error) {
            console.error('Copying to clipboard failed:', error);
        }
    };
    useEffect(() => {
        const showAlertAfterDelay = () => {
            setCopied(false);
        };
        const timer = setTimeout(showAlertAfterDelay, 3000);
        return () => clearTimeout(timer);
    }, [copied]);

    return { copy, copied };
};



type SessionStorageState<T> = {
    value: T | null;
    setValue: (value: T) => void;
    clearValue: () => void;
};

export const useCustomSessionStorage = <T>(key: string, initialValue: T | null): SessionStorageState<T> => {
    // Get the initial value from sessionStorage or use the provided initialValue
    const storedValue = sessionStorage.getItem(key);
    const initial = storedValue ? JSON.parse(storedValue) : initialValue;

    // Create a state to manage the value
    const [value, setValue] = useState<T | null>(initial);

    // Update the sessionStorage whenever the state changes
    const setAndStoreValue = (newValue: T) => {
        setValue(newValue);
        sessionStorage.setItem(key, JSON.stringify(newValue));
    };

    // Clear the value from sessionStorage
    const clearValue = () => {
        setValue(null);
        sessionStorage.removeItem(key);
    };

    return { value, setValue: setAndStoreValue, clearValue };
};


export function useScript(src: string) {
    const [scriptLoaded, setScriptLoaded] = useState(false);

    useEffect(() => {
        const script = document.createElement('script');
        script.src = src;
        script.async = true;

        const handleLoad = () => {
            setScriptLoaded(true);
        };

        const handleError = (error: any) => {
            console.error('Failed to load the script:', error);
        };

        script.onload = handleLoad;
        script.onerror = handleError;

        document.body.appendChild(script);

        return () => {
            script.onload = null;
            script.onerror = null;
        };
    }, [src]);

    return scriptLoaded;
}



