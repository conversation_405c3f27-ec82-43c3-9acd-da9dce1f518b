import React, { useContext, useEffect, useRef, useState } from 'react';
import { LanguageContext } from '../../context/LanguageProvider';
import { useAppContext } from '../../hooks';
import { isiPhone } from '../../utils';
import Loading from '../Loading';
import StepChecking from '../StepChecking';
import Alert from './Alert';

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function QRscan({ setIsShowModal }: Props) {
  const [sdkQR, setSdkQR] = useState<any>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [timeoutId, setTimeoutId] = useState<any>(null); // Added timeoutId state variable
  const [isRunning, setIsRunning] = useState(true);
  const [openAlert, setOpenAlert] = useState<boolean>(false);
  const [selectedVideoSource, setSelectedVideoSource] = useState<
    string | undefined
  >(undefined);

  const { setDataCapture, dataCapture, setScreenShow } = useAppContext();
  //get data context language
  const { dictionary } = useContext(LanguageContext);

  const process = async () => {
    if (isRunning && canvasRef.current) {
      const startDate = new Date();
      if (!canvasRef.current) return;
      if (!videoRef.current) return;
      const context: CanvasRenderingContext2D | null =
        canvasRef.current.getContext('2d');
      canvasRef.current.width = videoRef.current.videoWidth;
      canvasRef.current.height = videoRef.current.videoHeight;

      if (!context) return;
      // Calculate the dimensions for cropping the center
      const cropWidth = 320; // Set your desired crop width here
      const cropHeight = 320; // Set your desired crop height here

      // Calculate the position to crop from the center
      const cropX = (videoRef.current.videoWidth - cropWidth) / 2;
      const cropY = (videoRef.current.videoHeight - cropHeight) / 2;

      // Set the canvas dimensions to match the crop size
      canvasRef.current.width = cropWidth;
      canvasRef.current.height = cropHeight;

      // Clear the canvas
      context.clearRect(0, 0, cropWidth, cropHeight);

      // Draw the cropped frame from videoRef.current onto the canvas
      context.drawImage(
        videoRef.current,
        cropX,
        cropY,
        cropWidth,
        cropHeight,
        0,
        0,
        cropWidth,
        cropHeight,
      );

      sdkQR.processQr(canvasRef.current).then((resultQr: any) => {
        // const endDate = new Date();
        // const seconds = (endDate.getTime() - startDate.getTime()) / 1000;
        if (resultQr != null) {
          // check qr code cccd by regex
          const qrDataPattern =
            /^(\d{12})\|(\d{9})\|([^|]+)\|(\d{8})\|(Nam|Nữ)\|([^|]+)\|(\d{8})$/;
          const match = qrDataPattern.exec(resultQr.data);

          if (match) {
            setDataCapture({
              ...dataCapture,
              QRscan: resultQr.data || 'no data',
            });

            handleStopScan();
            stopStream();
            setScreenShow('PRERARE_CAPTURE');
          } else {
            setOpenAlert(true);
          }
        } else {
          console.log('Fail');
        }
      });
      const newTimeoutId = setTimeout(() => {
        requestAnimationFrame(process);
      }, 300);
      setTimeoutId(newTimeoutId);
    }
  };

  const stopStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => {
        track.stop();
      });
    }
  };

  const handleStopScan = () => {
    setIsRunning(false);
    // Clear the timeout when stopping the video processing
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
  };

  const gotDevices = (deviceInfos: MediaDeviceInfo[]) => {
    if (!deviceInfos) return;
    if (isiPhone()) {
      deviceInfos.forEach((deviceInfo) => {
        if (deviceInfo.kind === 'videoinput') {
          if (
            deviceInfo.label.toLowerCase() === 'back camera' ||
            deviceInfo.label === 'camera mặt sau'
          ) {
            setSelectedVideoSource(deviceInfo.deviceId);
          }
        }
      });
    } else {
      deviceInfos.forEach((deviceInfo) => {
        if (deviceInfo.kind === 'videoinput') {
          if (deviceInfo.label.toLowerCase() === 'camera2 0, facing back') {
            setSelectedVideoSource(deviceInfo.deviceId);
          }
        }
      });
    }
  };

  const gotStream = async (stream: MediaStream) => {
    if (videoRef.current) {
      videoRef.current.srcObject = stream;
      videoRef.current.play().catch(console.error);
    }

    streamRef.current = stream; // Store the stream in the ref

    try {
      const deviceInfos = await navigator.mediaDevices.enumerateDevices();
      return gotDevices(deviceInfos);
    } catch (error) {
      alert(
        'Camera không được hỗ trợ, vui lòng cấp quyền truy cập camera hoặc kiểm tra lại thiết bị của bạn!',
      );
      window.location.reload();
    }
  };
  const start = () => {
    setIsLoading(true);
    stopStream();
    const constraints: MediaStreamConstraints = {
      audio: false,
      video: {
        facingMode: 'environment',
        aspectRatio: 16 / 9,
        width: {
          ideal: 1920,
        },
        height: {
          ideal: 1080,
        },
        deviceId: selectedVideoSource
          ? { exact: selectedVideoSource }
          : undefined,
      },
    };

    navigator.mediaDevices
      ?.getUserMedia(constraints)
      .then(gotStream)
      .then(gotDevices as any)
      .catch((e) => {
        alert(
          'Camera không được hỗ trợ, vui lòng cấp quyền truy cập camera hoặc kiểm tra lại thiết bị của bạn!',
        );
        window.location.reload();
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  useEffect(() => {
    const getSDK = async () => {
      const moduleScanQR = await await (window as any).VNPTQRBrowserSDK;
      moduleScanQR.createInstance();
      setSdkQR(moduleScanQR);
    };
    getSDK();
  }, []);

  useEffect(() => {
    if (sdkQR) {
      process();
    }
  }, [sdkQR, openAlert]);
  useEffect(() => {
    start();
  }, [selectedVideoSource, openAlert]);

  return (
    <>
      {openAlert ? (
        <Alert dictionary={dictionary} closeAlert={() => setOpenAlert(false)} />
      ) : (
        <div className="vnpt-mx-auto vnpt-max-w-sm vnpt-mt-8">
          <div>
            <h2 className="vnpt-text-primary vnpt-uppercase vnpt-font-open vnpt-text-h-xs vnpt-text-center">
              QR CODE
            </h2>
          </div>
          <StepChecking numberStep={1} numberTotalStep={5} />
          <canvas className="vnpt-hidden" ref={canvasRef} id="canvas">
            {' '}
          </canvas>
          <div className="vnpt-relative vnpt-mx-auto vnpt-mt-6 vnpt-h-64 vnpt-w-64 vnpt-rounded-lg vnpt-overflow-hidden vnpt-flex vnpt-justify-center vnpt-items-center">
            {isLoading ? (
              <Loading />
            ) : (
              <div className="box blink vnpt-absolute vnpt-top-14 vnpt-left-14 vnpt-right-14 vnpt-bottom-14"></div>
            )}
            <video
              playsInline
              ref={videoRef}
              id="video"
              className="vnpt-w-full vnpt-h-full vnpt-object-cover"
            >
              Video stream not available!
            </video>
          </div>
          <div className="vnpt-mt-6">
            <p className="vnpt-text-center vnpt-font-semibold vnpt-text-gray-0 vnpt-text-base vnpt-font-open">
              {dictionary['QR_guide']}
            </p>
          </div>
          <div className="vnpt-flex vnpt-justify-center vnpt-mt-20">
            <div
              onClick={() => setIsShowModal(true)}
              className="vnpt-text-center"
            >
              <p className="vnpt-text-primary vnpt-font-bold vnpt-font-open vnpt-text-lg vnpt-underline vnpt-inline-block vnpt-cursor-pointer">
                {dictionary['instruction']}
              </p>
            </div>
            {/* <div onClick={() => { setScreenShow('QR_UPLOAD'); stopBothVideoAndAudio(streams) }} className='vnpt-text-center' >
                    <p className='vnpt-text-primary vnpt-font-bold vnpt-font-open vnpt-text-lg vnpt-underline vnpt-inline-block vnpt-cursor-pointer'>Quay lại</p>
                </div> */}
          </div>
        </div>
      )}
    </>
  );
}
