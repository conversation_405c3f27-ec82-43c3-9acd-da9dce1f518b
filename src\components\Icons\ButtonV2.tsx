import React from 'react'

type Props = {
    label: string
    className?: string
    icon?: React.JSX.Element;
    action?: (data?: any) => void;
    disable?: boolean
    hideLable?: boolean
} & JSX.IntrinsicElements['div']

export default function ButtonV2({ label, action, icon, className, disable, hideLable = false, ...props }: Props) {
    return (
        <div {...props} onClick={action} className={`vnpt-border sm:vnpt-w-52 vnpt-flex vnpt-justify-center vnpt-items-center vnpt-border-primary ${disable ? 'vnpt-cursor-not-allowed' : 'vnpt-cursor-pointer'} vnpt-rounded-lg vnpt-bg-gray-0 hover:vnpt-bg-primary vnpt-flex vnpt-items-center vnpt-justify-center vnpt-p-2 ${className}`}>
            {icon && <div>{icon}</div>}
            <p className={`${disable ? 'vnpt-text-gray-500' : 'vnpt-text-gray-900'} vnpt-ml-3 vnpt-text-base vnpt-font-semibold vnpt-font-open vnpt-uppercase ${hideLable && 'vnpt-hidden sm:vnpt-block'}`}>{label}</p>
        </div>
    )
}
