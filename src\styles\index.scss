@tailwind base;
@tailwind components;
@tailwind utilities;
/* @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap'); */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&family=Roboto:wght@100;300;400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

.bg-hero {
  background-image: linear-gradient(180deg, var(--vnpt-background-sdk) 70%, rgba(15, 43, 59, 0.8)),
    url(/lib/bg-vnpt.svg);
}

.bg-sdk {
  min-height: 100vh;
  background-color: var(--vnpt-background-sdk);
}

:root {
  --vnpt-primary-color: #18d696;
  --vnpt-text-color-default: #ffffff;
  --vnpt-background-sdk: #0f2b3b;
}

body {
  margin: 0;
}

.vnpt-ekyc-sdk-container {
  font-family: Roboto, sans-serif, system-ui;
  -webkit-tap-highlight-color: transparent;

  // start reset CSS
  blockquote,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  figure,
  p,
  pre {
    margin: 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  ol,
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  img,
  svg,
  video,
  // canvas,
  audio,
  iframe,
  embed,
  object {
    display: block;
    vertical-align: middle;
  }

  img,
  video {
    max-width: 100%;
    height: auto;
  }

  *,
  ::before,
  ::after {
    // border-width: 0;
    // border-style: solid;
    // border-color: theme('borderColor.DEFAULT', currentColor);
  }

  .google-map * {
    border-style: none;
  }

  // end reset CSS

  .text-balance {
    text-wrap: balance;
  }

  .title {
    @apply vnpt-text-primary vnpt-uppercase vnpt-font-open vnpt-text-h-xs vnpt-text-center;
  }

  .description {
    @apply vnpt-text-center vnpt-font-semibold vnpt-text-gray-0 vnpt-text-base vnpt-font-open;
  }

  .center-position {
    @apply vnpt-absolute vnpt--translate-x-1/2 vnpt--translate-y-1/2 vnpt-top-1/2 vnpt-left-1/2;
  }

  /* start loading component */
  .one {
    position: absolute;
    border-radius: 50%;
    opacity: 0;
    animation: loading 1.3s 0.65s infinite;
  }

  .two {
    position: absolute;
    border-radius: 50%;
    opacity: 0;
    animation: loading 1.3s infinite;
  }

  /* start warning face verify */
  .warning-message {
    background-color: rgb(24, 70, 147, 1);
    color: white;
    border-radius: 6px;
    animation: pulse 0.3s infinite;
  }

  .box {
    --b: 3px;
    /* thickness of the border */
    --c: #184693;
    /* color of the border */
    --w: 40px;
    /* width of border */
    --r: 4px;

    border: var(--b) solid #0000;
    /* space for the border */
    --_g: #0000 90deg, var(--c) 0;
    --_p: var(--w) var(--w) border-box no-repeat;
    background: conic-gradient(from 90deg at top var(--b) left var(--b), var(--_g)) 0 0 / var(--_p),
      conic-gradient(from 180deg at top var(--b) right var(--b), var(--_g)) 100% 0 / var(--_p),
      conic-gradient(from 0deg at bottom var(--b) left var(--b), var(--_g)) 0 100% / var(--_p),
      conic-gradient(from -90deg at bottom var(--b) right var(--b), var(--_g)) 100% 100% / var(--_p);

    /*Irrelevant code*/
    box-sizing: border-box;
    margin: 5px;
    display: inline-flex;
    font-size: 30px;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-radius: var(--r);
  }

  .blink {
    animation: blink-animation 300ms steps(5, start) infinite;
    -webkit-animation: blink-animation 300ms steps(5, start) infinite;
  }

  @keyframes pulse {
    10% {
      box-shadow: 0 0 0 2px rgba(24, 70, 147, 1);
    }

    25% {
      box-shadow: 0 0 0 4px rgb(24, 70, 147, 0.75);
    }

    50% {
      box-shadow: 0 0 0 6px rgb(24, 70, 147, 0.5);
    }

    75% {
      box-shadow: 0 0 0 8px rgb(24, 70, 147, 0.25);
    }

    100% {
      box-shadow: 0 0 0 10px rgb(24, 70, 147, 0.1);
    }
  }

  @keyframes loading {
    0% {
      opacity: 0;
      transform: scale(0.15);
      box-shadow: 0 0 2px rgba(black, 0.1);
    }

    25% {
      opacity: 0;
      transform: scale(1);
      box-shadow: 0 0 5px rgba(black, 0.1);
    }

    50% {
      opacity: 1;
      transform: scale(2);
      box-shadow: 0 0 10px rgba(black, 0.1);
    }

    75% {
      opacity: 1;
      transform: scale(0.5);
      box-shadow: 0 0 5px rgba(black, 0.1);
    }

    100% {
      opacity: 0;
      transform: scale(0.15);
      box-shadow: 0 0 2px rgba(black, 0.1);
    }
  }

  @keyframes blink-animation {
    to {
      visibility: hidden;
    }
  }

  @-webkit-keyframes blink-animation {
    to {
      visibility: hidden;
    }
  }
}

.ant-modal-content {
  padding: 0 !important;
  border-radius: 20px !important;
  overflow: hidden;
}
