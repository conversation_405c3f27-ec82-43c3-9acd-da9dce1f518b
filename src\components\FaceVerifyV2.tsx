declare global {
  interface Window {
    destroyTimeout: () => void;
  }
}

import { Modal } from 'antd';
import bodymovin from 'lottie-web';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { DataLivenessImg, compareFaceMobile, liveness3DWeb, maskedFaceMobile, uploadImage } from '../api';
import { VERSION } from '../constants';
import { LanguageContext } from '../context/LanguageProvider';
import { b64toBlob } from '../helpers';
import { useAppContext } from '../hooks';
import { IDataHash, ModalType, TYPE_DOCUMENT } from '../types';
import {
  changeThemeDefault,
  defineTransaction,
  drawImageOVal,
  getImageReduceSize,
  handleTurnOnWebCam,
  onShowResult,
  stopBothVideoAndAudio,
} from '../utils';
import CloseFailed from './Icons/CloseFailed';
import Loading from './Loading';
const PROCESSING_DELAY = 1000;

const MESSAGE_FACE = {
  FIT: 'FIT',
  TO_THE_RIGHT: 'TO_THE_RIGHT',
  TO_THE_LEFT: 'TO_THE_LEFT',
  TO_THE_BOTTOM: 'TO_THE_BOTTOM',
  TO_THE_TOP: 'TO_THE_TOP',
  NOT_STRAIGHT: 'NOT_STRAIGHT',
  TOO_FAR: 'TOO_FAR',
  TOO_NEAR: 'TOO_NEAR',
  INVALID: 'INVALID',
  NO_FACE: 'NO_FACE',
  TIME_EXCEED: 'TIME_EXCEED',
};

type faceVerifyProps = {
  dataHash: IDataHash;
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  typeDocument: TYPE_DOCUMENT | undefined;
  setNameModal: React.Dispatch<React.SetStateAction<ModalType>>;
};
function FaceVerify({ dataHash, setIsShowModal, typeDocument, setNameModal }: faceVerifyProps) {
  // variables check
  let STABLE_FACE_NUM = 2;
  let validCount = 0;
  let isTakePicture = true;
  let stateAnimation = 'FAR';
  let b64ImageOvalFar = '';

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [streams, setStreams] = useState<MediaStream[]>([]);
  const [warningMessage, setWarningMessage] = useState<string>(
    'Di chuyển di động sao cho gương mặt nằm trong vòng tròn và thực hiện chuyển động theo hướng dẫn',
  );
  const { dataCapture, setDataCapture, dataConfig, setScreenShow } = useAppContext();
  const [loadingCam, setLoadingCam] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const videoChunksRef = useRef<BlobPart[]>([]);
  const [isShowExceedTimeModal, setIsShowExceedTimeModal] = useState(false);
  const client_session = defineTransaction(VERSION);
  const [faceSDK, setFaceSDK] = useState<any>(null);
  const [animation, setAnimation] = useState<any>(null);
  const timeoutExceedModalRef = useRef<NodeJS.Timeout | null>(null);
  const intervalScanFaceRef = useRef<NodeJS.Timeout | null>(null);

  //get data context language
  const { dictionary } = useContext(LanguageContext);
  const messages = dictionary['face_verify'];
  const default_status = messages['default_status'];
  const error_status = messages['error_status'];
  const MESSAGE_NOTICE: any = {
    FIT: messages['face_steady'],
    TO_THE_RIGHT: default_status,
    TO_THE_LEFT: default_status,
    TO_THE_BOTTOM: default_status,
    TO_THE_TOP: default_status,
    NOT_STRAIGHT: default_status,
    TOO_FAR: messages['closer'],
    TOO_NEAR: messages['furthur'],
    INVALID: default_status,
    NO_FACE: default_status,
    TIME_EXCEED: error_status,
    'Library not loaded': error_status,
    'Image format error': default_status,
  };

  const handleStartRecording = () => {
    try {
      // Tìm stream còn active
      const activeStream = streams.find((stream) => stream.active);

      if (!activeStream) {
        console.log('🚫 Không có stream nào còn active');
        return;
      }

      const options = { mimeType: 'video/mp4' }; // Đổi sang video/mp4 để hỗ trợ iOS
      const mediaRecorder = new MediaRecorder(activeStream, options);
      mediaRecorderRef.current = mediaRecorder;

      videoChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          videoChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.start();
    } catch (error) {
      console.log('🚀 ~ handleStartRecording ~ error:', error);
    }
  };

  const handleStopRecording = (): Promise<Blob> => {
    return new Promise((resolve) => {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.onstop = () => {
          // Sử dụng MP4 thay vì WebM
          const videoBlob = new Blob(videoChunksRef.current, { type: 'video/mp4' });
          videoChunksRef.current = []; // Clear chunks
          resolve(videoBlob);
        };
        mediaRecorderRef.current.stop();
      } else {
        resolve(new Blob());
      }
    });
  };

  async function process(FaceVNPTBrowserSDK: any) {
    try {
      if (!isTakePicture) return;

      const windowWidth = window.screen.width < window.outerWidth ? window.screen.width : window.outerWidth;
      const isMobile = windowWidth < 1024;
      let imgD = null;
      let response = null;

      if (videoRef.current && canvasRef.current) {
        const percent_temp = stateAnimation === 'FAR' ? (isMobile ? 0.6 * 1.5 * 0.75 : 0.4) : isMobile ? 1 : 0.5;
        imgD = await drawImageOVal(
          percent_temp,
          stateAnimation === 'FAR' ? 0.75 : 1,
          videoRef.current,
          canvasRef.current,
        );
      }

      response = await FaceVNPTBrowserSDK.processFace(imgD, isMobile ? 2 : 1);
      setWarningMessage(MESSAGE_NOTICE[response]);

      if (response === MESSAGE_FACE.FIT) {
        validCount++;
      } else if (response === MESSAGE_FACE.TIME_EXCEED) {
        handleTimeExceed(FaceVNPTBrowserSDK);
      } else {
        validCount = 0;
      }

      if (validCount > STABLE_FACE_NUM) {
        await handleValidCountExceed(FaceVNPTBrowserSDK);
      }
    } catch (error) {
      handleError(error);
    }
  }

  const handleTimeExceed = (FaceVNPTBrowserSDK: any) => {
    stopInterval();
    isTakePicture = false;
    FaceVNPTBrowserSDK.free();
    stopBothVideoAndAudio(streams);
    // if (dataConfig.HAS_RESULT_SCREEN) {
    //   setScreenShow('VALIDATIONS_RESULTS');
    // }
    setIsShowExceedTimeModal(true); // Show the exceed modal
  };

  const handleValidCountExceed = async (FaceVNPTBrowserSDK: any) => {
    let dataLivenessImg: DataLivenessImg = { near_img: '', far_img: '', scan3d: '' };

    if (stateAnimation === 'FAR' && dataConfig.DOUBLE_LIVENESS) {
      await handleFarState(FaceVNPTBrowserSDK);
    } else {
      await handleNearState(FaceVNPTBrowserSDK, dataLivenessImg);
    }
  };

  const handleFarState = async (FaceVNPTBrowserSDK: any) => {
    FaceVNPTBrowserSDK.notifyCapture();
    animation?.playSegments([0, 170], true);
    animation?.setSpeed(2);

    if (videoRef.current && canvasRef.current) {
      const img_canvas_oval = await drawImageOVal(1, 1, videoRef.current, canvasRef.current);
      b64ImageOvalFar = getImageReduceSize(img_canvas_oval as HTMLCanvasElement, dataConfig.MAX_SIZE_IMAGE);
    }

    validCount = 0;
    stateAnimation = 'NEAR';
    isTakePicture = false;
    setTimeout(() => {
      isTakePicture = true;
    }, PROCESSING_DELAY);
  };

  const handleNearState = async (FaceVNPTBrowserSDK: any, dataLivenessImg: DataLivenessImg) => {
    setLoadingCam(false);
    FaceVNPTBrowserSDK.notifyCapture();
    isTakePicture = false;
    animation?.playSegments([170, 300], true);
    animation.setSpeed(1.5);

    if (dataConfig.CHECK_LIVENESS_FACE) {
      // nếu bật check liveness face thì mới upload ảnh 3D để call api liveness3DWeb
      const maks3D = FaceVNPTBrowserSDK.getResult();
      const file3D = new Blob([maks3D], { type: 'text' });
      const res3dImg = await uploadImage(file3D, dataConfig);
      if (res3dImg.error) throw new Error(res3dImg.error);
      dataLivenessImg.scan3d = res3dImg.object.hash;
    } else {
      dataLivenessImg.scan3d = '';
    }

    if (videoRef.current && canvasRef.current) {
      const img_canvas_oval = await drawImageOVal(1, 1, videoRef.current, canvasRef.current);
      videoRef.current.src = '';
      const b64ImageOvalNear = getImageReduceSize(img_canvas_oval as HTMLCanvasElement, dataConfig.MAX_SIZE_IMAGE);
      await handleImageUpload(b64ImageOvalNear, dataLivenessImg);
    }

    if (dataConfig.DOUBLE_LIVENESS) {
      FaceVNPTBrowserSDK.free();
      stopInterval();
    } else {
      setLoadingCam(false);
      validCount = 0;
      isTakePicture = true;
    }
    stopBothVideoAndAudio(streams);
  };

  const handleImageUpload = async (b64ImageOvalNear: string, dataLivenessImg: DataLivenessImg) => {
    setIsProcessing(true);

    const nearImgRes = await uploadImage(b64toBlob(b64ImageOvalNear), dataConfig);
    if (nearImgRes.error) throw new Error(nearImgRes.error);

    if (dataConfig.DOUBLE_LIVENESS) {
      const farImgRes = await uploadImage(b64toBlob(b64ImageOvalFar), dataConfig);
      if (farImgRes.error) throw new Error(farImgRes.error);
      dataLivenessImg.far_img = farImgRes.object.hash;
    } else {
      dataLivenessImg.far_img = nearImgRes.object.hash;
    }

    const imgHashNearImg = nearImgRes.object.hash;
    dataLivenessImg.near_img = imgHashNearImg;

    if (imgHashNearImg) {
      await handleFinalProcessing(imgHashNearImg, b64ImageOvalNear, dataLivenessImg);
    }
  };

  const handleFinalProcessing = async (
    imgHashNearImg: string,
    b64ImageOvalNear: string,
    dataLivenessImg: DataLivenessImg,
  ) => {
    const compareParams = { hashFront: dataHash.hashFront, hashFace: imgHashNearImg };

    try {
      const videoBlob = await handleStopRecording();
      const [livenessFaceRes, compareFaceRes, maskedFaceRes] = await Promise.allSettled([
        dataConfig.CHECK_LIVENESS_FACE
          ? liveness3DWeb(dataLivenessImg, dataConfig, client_session)
          : Promise.resolve(false),
        dataConfig.COMPARE_FACE ? compareFaceMobile(compareParams, dataConfig, client_session) : Promise.resolve(false),
        dataConfig.CHECK_MASKED_FACE
          ? maskedFaceMobile({ hashFace: imgHashNearImg }, dataConfig, client_session)
          : Promise.resolve(false),
      ]);

      if (
        livenessFaceRes.status === 'fulfilled' &&
        compareFaceRes.status === 'fulfilled' &&
        maskedFaceRes.status === 'fulfilled'
      ) {
        const dataUpdated = {
          ...dataCapture,
          hash_img: imgHashNearImg,
          base64_doc_img: {
            img_front: dataCapture.base64_doc_img.img_front,
            img_back: dataCapture.base64_doc_img.img_back,
          },
          base64_face_img: { img_face_far: b64ImageOvalFar, img_face_near: b64ImageOvalNear },
          liveness_face: livenessFaceRes.value,
          compare: compareFaceRes.value,
          maskedFaceRes: maskedFaceRes.value,
          videoRecorded: videoBlob,
        };

        setDataCapture(dataUpdated);
        setWarningMessage('Hoàn thành');
        if (dataConfig.HAS_RESULT_SCREEN) {
          setScreenShow('VALIDATIONS_RESULTS');
        } else {
          onShowResult(dataConfig, dataUpdated);
        }
      }
    } catch (error) {
      console.log('Error:', error);
    } finally {
      setIsProcessing(false);
      stopBothVideoAndAudio(streams);

      // clear timeout limit exceed face scan
      if (timeoutExceedModalRef.current) {
        clearTimeout(timeoutExceedModalRef.current);
      }
      console.log('⏱️ đã clear timeout!');
    }
  };

  const handleError = (error: any) => {
    console.log('🚀 ~ process ~ error:', error);
    setNameModal('result');
    stopBothVideoAndAudio(streams);
    setIsShowModal(true);
  };

  const onCheckCamera = () => {
    async function onStartWebcam() {
      setLoadingCam(true);
      await handleTurnOnWebCam({
        videoRef: videoRef.current,
        streams: streams,
        turnOnFrontCam: true, // true for turn on front camera
        fakeCamLabel: dataConfig.FAKE_CAM_LABEL,
      });
      setLoadingCam(false);
      handleStartRecording();
    }
    onStartWebcam();
  };

  const onCheckSizeScreen = () => {
    // check size screen to get oval json
    const windowWidth = window.screen.width < window.outerWidth ? window.screen.width : window.outerWidth;
    let oval: any = dataConfig.URL_WEB_OVAL || '/lib/web-oval-hdss.json';
    if (windowWidth < 1024) {
      oval = dataConfig.URL_MOBILE_OVAL || '/lib/mobile-oval-1.json';
    }
    const animation = bodymovin.loadAnimation({
      container: document.querySelector('#animation') as any,
      path: oval, // Required
      renderer: 'svg', // Required
      loop: false, // Optional
      autoplay: false, // Optional
      name: 'oval', // Name for future reference. Optional.
    });
    setAnimation(animation);
  };

  const onCheckFaceSDK = () => {
    const getSDK = async () => {
      const sdkLivenessFace = await (window as any).FaceVNPTBrowserSDK;
      sdkLivenessFace.init();
      setFaceSDK(sdkLivenessFace);
    };
    getSDK();
  };

  useEffect(() => {
    onCheckCamera();
    onCheckSizeScreen();
    onCheckFaceSDK();
  }, []);

  useEffect(() => {
    if (!isShowExceedTimeModal) {
      // chỉ chạy khi modal không hiển thị
      timeoutExceedModalRef.current = setTimeout(() => {
        isTakePicture = false;
        stopInterval();
        stopBothVideoAndAudio(streams);
        setIsShowExceedTimeModal(true); // Show the exceed modal
      }, dataConfig.TIME_EXCEED_FACE_LIVENESS * 1000); // convert seconds to milliseconds

      return () => {
        if (timeoutExceedModalRef.current) {
          console.log('⏱️ đã clear timeout!');
          clearTimeout(timeoutExceedModalRef.current);
        }
      };
    }
  }, [isShowExceedTimeModal]);

  const stopInterval = () => {
    if (intervalScanFaceRef.current) {
      clearInterval(intervalScanFaceRef.current);
    }
  };
  useEffect(() => {
    intervalScanFaceRef.current = setInterval(() => {
      if (faceSDK && isTakePicture) {
        process(faceSDK);
      }
    }, 350);
    return () => {
      if (intervalScanFaceRef.current) clearInterval(intervalScanFaceRef.current);
    };
  }, [faceSDK]);

  const ExceededTimeModal = () => {
    return (
      <Modal
        zIndex={9999999999999}
        footer={null}
        closable={false}
        maskClosable={false}
        open={isShowExceedTimeModal}
        onCancel={() => setIsShowExceedTimeModal(false)}
        destroyOnClose
      >
        <div className="vnpt-flex vnpt-flex-col vnpt-items-center vnpt-p-4 vnpt-mx-auto">
          <div className="vnpt-hidden">
            <style dangerouslySetInnerHTML={changeThemeDefault(dataConfig)} />
          </div>
          <div className="vnpt-w-full vnpt-max-w vnpt-border-t vnpt-border-t-gray-400 vnpt-pt-2">
            <div className="vnpt-flex vnpt-justify-center vnpt-items-center">
              <CloseFailed />
            </div>
            <div className="vnpt-pb-4 vnpt-border-solid vnpt-border-0 vnpt-border-b vnpt-border-b-gray-400">
              <div className="vnpt-flex vnpt-flex-col vnpt-items-center vnpt-gap-4">
                <span className="vnpt-font-bold vnpt-text-md vnpt-font-inter vnpt-mt-2">Thông báo</span>
                <p className="vnpt-font-normal vnpt-text-base vnpt-m-0 vnpt-font-inter vnpt-text-center">
                  Đã quá thời gian thao tác.
                  <br /> Quý khách vui lòng thử lại!
                </p>
              </div>
            </div>
          </div>

          <div className="vnpt-flex vnpt-gap-4 vnpt-mt-4 vnpt-justify-center">
            <div
              onClick={() => {
                setIsShowExceedTimeModal(false);
                // Reset all states
                validCount = 0;
                isTakePicture = true;
                stateAnimation = 'FAR';
                b64ImageOvalFar = '';
                setLoadingCam(false);
                setIsProcessing(false);
                setFaceSDK(null);
                stopInterval();
                stopBothVideoAndAudio(streams);

                // Reset animation
                animation?.stop();
                animation?.destroy();
                setAnimation(null);

                onCheckCamera();
                onCheckSizeScreen();
                onCheckFaceSDK();
              }}
              className="sm:vnpt-w-52 vnpt-text-center vnpt-py-2 vnpt-px-8 vnpt-bg-primary vnpt-rounded-full vnpt-text-base vnpt-font-medium vnpt-font-inter vnpt-text-white vnpt-cursor-pointer"
            >
              Thử lại
            </div>
          </div>
        </div>
      </Modal>
    );
  };

  window.destroyTimeout = () => {
    if (timeoutExceedModalRef.current) {
      stopInterval();
      stopBothVideoAndAudio(streams);
      clearTimeout(timeoutExceedModalRef.current);
      console.log('⏱️ Cleared timeout from destroyTimeout method');
    }
  };

  return (
    <>
      {/* modal */}
      <ExceededTimeModal />

      {/* content */}
      <div>
        <div className="vnpt-flex vnpt-flex-col vnpt-bg-transparent vnpt-justify-center vnpt-items-center vnpt-mx-auto vnpt-mt-8">
          <div className="sm:vnpt-w-128 vnpt-text-center">
            <p className="sm:vnpt-block vnpt-hidden vnpt-font-semibold vnpt-font-inter vnpt-text-[22px] vnpt-mb-4">
              Chụp khuôn mặt
            </p>
            <p className="vnpt-text-balance vnpt-text-base vnpt-font-normal vnpt-font-inter vnpt-text-center vnpt-mb-4">
              Di chuyển di động sao cho gương mặt nằm trong vòng tròn và thực hiện chuyển động theo hướng dẫn
            </p>
          </div>
          <canvas className="vnpt-hidden" ref={canvasRef} id="canvas" />
          <div className="vnpt-flex vnpt-flex-col vnpt-mx-auto vnpt-mt-6 vnpt-rounded-lg vnpt-justify-center vnpt-items-center">
            {loadingCam || isProcessing ? (
              <Loading hasText={isProcessing} />
            ) : (
              <div className="warning-message vnpt-w-full vnpt-py-1 vnpt-px-3 vnpt-z-20 sm:-vnpt-mb-4 -vnpt-mb-14">
                <div className="vnpt-text-base vnpt-text-center vnpt-font-medium vnpt-font-inter vnpt-text-white">
                  {warningMessage}
                </div>
              </div>
            )}
            <div className="vnpt-relative vnpt-overflow-hidden vnpt-z-10">
              <div id="animation" />
              <video
                playsInline
                className="-vnpt-scale-x-100 vnpt-absolute vnpt-inset-0 -vnpt-z-10 sm:vnpt-p-1"
                id="video"
                ref={videoRef}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
export default FaceVerify;
