import * as React from 'react';
const Step1_QRscan = ({ des }: { des: string }) => (
  <div className="vnpt-flex vnpt-justify-center vnpt-items-center vnpt-flex-col vnpt-gap-2">
    <svg
      className="vnpt-w-20 sm:vnpt-w-auto"
      width="102"
      height="81"
      viewBox="0 0 102 81"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_6825_23045"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="80"
        height="52"
      >
        <rect width="80" height="52" fill="#C4C4C4" />
      </mask>
      <g mask="url(#mask0_6825_23045)">
        <path
          d="M62.0623 0H-125.859C-136.238 0 -144.651 8.41362 -144.651 18.7922V124.434C-144.651 134.813 -136.238 143.226 -125.859 143.226H62.0623C72.4412 143.226 80.8545 134.813 80.8545 124.434V18.7922C80.8545 8.41362 72.4412 0 62.0623 0Z"
          fill="#B6D6C5"
        />
        <path
          d="M56.7258 6.44824H-120.529C-129.87 6.44824 -137.442 14.0205 -137.442 23.3612V119.862C-137.442 129.202 -129.87 136.775 -120.529 136.775H56.7258C66.0655 136.775 73.6388 129.202 73.6388 119.862V23.3612C73.6388 14.0205 66.0655 6.44824 56.7258 6.44824Z"
          stroke="#D0EFDE"
          stroke-width="2"
        />
        <path
          d="M22.6372 19.3018H-61.0068V27.3445H22.6372V19.3018Z"
          fill="#E86C60"
        />
        <path
          d="M14.4337 35.3896H-52.8096V43.4323H14.4337V35.3896Z"
          fill="#E86C60"
        />
        <path
          d="M35.5049 43.4293H46.4722V32.4619H35.5049V43.4293ZM37.6982 34.6556H44.2786V41.219H37.6982V34.6556Z"
          fill="#2F4356"
        />
        <path
          d="M42.086 36.8486H39.8926V39.0423H42.086V36.8486Z"
          fill="#2F4356"
        />
        <path
          d="M55.2486 41.2354H53.0547V43.4288H55.2486V41.2354Z"
          fill="#2F4356"
        />
        <path
          d="M59.6353 41.2354H57.4414V43.4288H59.6353V41.2354Z"
          fill="#2F4356"
        />
        <path
          d="M57.439 34.6556H55.2457V32.4619H48.665V43.4293H50.8587V36.8489H53.0518V39.0426H59.6324V32.4619H57.439V34.6556Z"
          fill="#2F4356"
        />
        <path
          d="M35.5059 30.2703H46.4731V19.3027H35.5059V30.2703ZM37.6991 21.496H44.2796V28.0765H37.6991V21.496Z"
          fill="#2F4356"
        />
        <path
          d="M42.086 23.6895H39.8926V25.883H42.086V23.6895Z"
          fill="#2F4356"
        />
        <path
          d="M48.665 19.3008V30.2683H59.6326V19.3008H48.665ZM57.439 28.0745H50.8587V21.4941H57.439V28.0745Z"
          fill="#2F4356"
        />
        <path
          d="M55.2486 23.6895H53.0547V25.883H55.2486V23.6895Z"
          fill="#2F4356"
        />
      </g>
      <path
        d="M66.9997 25.0505V16C66.9997 13.7909 65.2089 12 62.9997 12H53.9492"
        stroke="#18D696"
        stroke-width="3"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M29.0003 36.9495L29.0003 46C29.0003 48.2091 30.7911 50 33.0003 50L42.0508 50"
        stroke="#18D696"
        stroke-width="3"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M29.0003 25.0505V16C29.0003 13.7909 30.7911 12 33.0003 12H42.0508"
        stroke="#18D696"
        stroke-width="3"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M66.9997 36.9495L66.9997 46C66.9997 48.2091 65.2089 50 62.9997 50L53.9492 50"
        stroke="#18D696"
        stroke-width="3"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M31.9463 31.6133H63.8173"
        stroke="#18D696"
        stroke-width="3"
        stroke-linecap="round"
      />
      <path
        d="M80.0298 54.0377C76.3888 51.6328 74.3374 50.2777 73.3443 49.6221C72.9078 49.3339 72.7527 48.7679 72.9804 48.2969L74.3167 45.5316C75.2439 43.6126 76.9246 42.1488 78.9261 41.4748C79.4422 41.301 80.0049 41.5611 80.2158 42.0632L82.7708 48.1524C83.003 48.7059 82.723 49.3439 82.1557 49.5399C81.9156 49.6229 81.6783 49.7137 81.4442 49.8122C80.8488 50.0626 80.5953 50.7698 80.8943 51.3423C81.1434 51.8193 81.727 52.0877 82.3298 51.8362C83.3096 51.4274 84.2157 52.5683 83.5791 53.418C82.7404 54.5375 81.1821 54.7988 80.0298 54.0377Z"
        fill="#EFA892"
      />
      <path
        d="M101.968 52.2511L100.132 79.2329C100.092 79.8128 99.6098 80.2429 99.0286 80.2429H78.2417C77.6315 80.2415 77.1386 79.7272 77.1386 79.1183C77.1386 76.8928 76.0051 74.8549 74.1067 73.6648C70.9368 71.6795 68.7471 68.5648 67.9421 64.8923C67.7668 64.0929 67.325 63.3791 66.6996 62.882L60.6151 58.0498C59.2869 56.995 58.9432 55.1022 59.8157 53.647C62.2314 49.6227 67.507 48.4117 71.4379 51.0086L78.8147 55.8808C80.7462 57.1579 83.3224 56.8804 84.9392 55.2209C86.2963 53.8292 86.6857 51.7707 85.9346 49.9786L73.7629 20.9728C73.23 19.704 73.2355 18.3082 73.7767 17.0435C75.3897 13.2839 80.5505 12.9483 82.6763 16.367L99.9426 44.1247C101.463 46.5698 102.163 49.3794 101.968 52.2511Z"
        fill="#EFA892"
      />
    </svg>
    <p className="vnpt-text-sm vnpt-font-open vnpt-font-normal vnpt-text-gray-900 vnpt-text-center">
      {des}
    </p>
  </div>
);
export default Step1_QRscan;
