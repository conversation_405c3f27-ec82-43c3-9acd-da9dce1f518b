import React, { useContext, useEffect } from 'react';
import { LIST_DOCUMENT_ID } from '../constants';
import { LanguageContext } from '../context/LanguageProvider';
import { useAppContext, useUploadImage } from '../hooks';
import { TYPE_DOCUMENT } from '../types';
import ButtonV2 from './Icons/ButtonV2';
import Camera from './Icons/Camera';
import Upload from './Icons/Upload';
import StepChecking from './StepChecking';

type Props = {
  setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  typeDocument: TYPE_DOCUMENT | undefined;
};

export default function PrepareCapture({ setIsShowModal, typeDocument }: Props) {
  const { handleFileChange, previewURL, selectedFile } = useUploadImage();
  const { dataCapture, setDataCapture, setScreenShow, dataConfig } = useAppContext();
  //get data context language
  const { dictionary } = useContext(LanguageContext);

  useEffect(() => {
    setDataCapture({
      ...dataCapture,
      base64_doc_img: { img_front: previewURL },
    });
    if (selectedFile) {
      handleNextPage();
    }
  }, [previewURL]);

  const handleNextPage = (): void => {
    if (typeDocument === 'one_side') {
      setScreenShow('CAPTURE_ONE_SIDE');
    }
    if (typeDocument === 'two_side') {
      setScreenShow('CAPTURE_TWO_SIDE');
    }
  };

  // check is new cccd
  const hasQR = dataCapture.type_document === LIST_DOCUMENT_ID.CITIZEN_CARD_ID_CHIP;

  return (
    <div className="vnpt-mx-auto vnpt-max-w-sm vnpt-mt-8">
      <div>
        <h2 className="vnpt-text-primary vnpt-uppercase vnpt-font-open vnpt-text-h-xs vnpt-text-center">
          {dictionary['take_front_side']}
        </h2>
      </div>
      <StepChecking
        numberStep={hasQR ? 2 : 1}
        numberTotalStep={typeDocument === 'one_side' ? (hasQR ? 4 : 3) : hasQR ? 5 : 4}
      />
      <div className="vnpt-mx-auto vnpt-mt-6 sm:vnpt-w-96 vnpt-h-60 vnpt-bg-empty-cam vnpt-rounded-lg vnpt-flex vnpt-flex-col vnpt-justify-center vnpt-items-center vnpt-mb-8">
        {/* allow take photo and upload */}
        {dataConfig.USE_METHOD === 'BOTH' && (
          <>
            <ButtonV2
              action={handleNextPage}
              className="vnpt-mb-8 vnpt-w-44 sm:vnpt-w-52"
              label={dictionary['take_photo']}
              icon={<Camera />}
            />
            <div className="vnpt-relative vnpt-overflow-hidden vnpt-inline">
              <input
                accept="image/*"
                onChange={handleFileChange}
                type="file"
                className="vnpt-absolute vnpt-inset-0 vnpt-opacity-0 vnpt-cursor-pointer hover:vnpt-bg-primary"
              />
              <ButtonV2 label={dictionary['upload_photo']} className="vnpt-w-44 sm:vnpt-w-52" icon={<Upload />} />
            </div>
          </>
        )}

        {/* only allow take photo */}
        {dataConfig.USE_METHOD === 'PHOTO' && (
          <ButtonV2
            action={handleNextPage}
            className="vnpt-mb-8 vnpt-w-44 sm:vnpt-w-52"
            label={dictionary['take_photo']}
            icon={<Camera />}
          />
        )}

        {/* only allow upload photo */}
        {dataConfig.USE_METHOD === 'UPLOAD' && (
          <div className="vnpt-relative vnpt-overflow-hidden vnpt-inline">
            <input
              accept="image/*"
              onChange={handleFileChange}
              type="file"
              className="vnpt-absolute vnpt-inset-0 vnpt-opacity-0 vnpt-cursor-pointer hover:vnpt-bg-primary"
            />
            <ButtonV2 label={dictionary['upload_photo']} className="vnpt-w-44 sm:vnpt-w-52" icon={<Upload />} />
          </div>
        )}
      </div>
      <div className="vnpt-mt-6">
        <p className="vnpt-text-center vnpt-font-semibold vnpt-text-gray-0 vnpt-text-base vnpt-font-open">
          {dictionary['guide_take_photo']}
        </p>
      </div>
      <div className="vnpt-mt-20 vnpt-text-center" onClick={() => setIsShowModal(true)}>
        <p className="vnpt-text-primary vnpt-font-bold vnpt-font-open vnpt-text-lg vnpt-underline vnpt-inline-block vnpt-cursor-pointer">
          {dictionary['instruction']}
        </p>
      </div>
    </div>
  );
}
