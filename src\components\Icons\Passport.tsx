import React from 'react'

type Props = {}

export default function Passport({ }: Props) {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M23.2856 1.78908C23.7258 2.22756 23.9999 2.81403 23.9999 3.4581V4.74962H24.0001V3.45829C24.0001 2.81413 23.7259 2.22758 23.2856 1.78908ZM27.2794 5.54191C27.7261 5.98909 28 6.58987 28 7.24962V28.4995C28 29.8783 26.804 30.9995 25.3333 30.9995H6.66668C5.90407 30.9995 5.21534 30.6981 4.72892 30.2155C5.21536 30.6982 5.90416 30.9997 6.66688 30.9997H25.3335C26.8042 30.9997 28.0002 29.8785 28.0002 28.4997V7.24981C28.0002 6.58997 27.7262 5.98911 27.2794 5.54191ZM3.7002 7.24981C3.7002 5.74797 4.94911 4.55824 6.4776 4.46547L20.6686 0.786678C20.896 0.732732 21.0982 0.702107 21.3306 0.699847C22.9084 0.684698 24.3001 1.90366 24.3001 3.45829V4.44981H25.3335C26.9514 4.44981 28.3001 5.68743 28.3002 7.24981V28.4997C28.3002 30.0622 26.9513 31.2997 25.3335 31.2997H6.66688C5.04897 31.2997 3.7002 30.0621 3.7002 28.4997V7.24981ZM13.9313 4.44981L21.1526 2.57758C21.7624 2.43339 22.3668 2.90712 22.3668 3.45829V4.44981H13.9313ZM22.3134 2.64158C22.0054 2.33605 21.5375 2.177 21.0802 2.28643L11.5795 4.74962H11.5786L21.08 2.28623C21.5374 2.17678 22.0054 2.33592 22.3134 2.64158ZM26.3668 28.4997C26.3668 29.0051 25.9214 29.4497 25.3335 29.4497H6.66688C6.07903 29.4497 5.63357 29.0051 5.63357 28.4997V7.24981C5.63357 6.74439 6.07903 6.29984 6.66688 6.29984H25.3335C25.9214 6.29984 26.3668 6.74439 26.3668 7.24981V28.4997ZM26.2758 6.36643C26.0343 6.13996 25.7009 5.99965 25.3333 5.99965H6.66668C5.93168 5.99965 5.33337 6.56056 5.33337 7.24962V28.4995C5.33337 28.8293 5.47041 29.1297 5.69389 29.3533C5.47053 29.1297 5.33357 28.8294 5.33357 28.4997V7.24981C5.33357 6.56076 5.93188 5.99984 6.66688 5.99984H25.3335C25.701 5.99984 26.0343 6.14007 26.2758 6.36643ZM16.0001 7.80029C11.846 7.80029 8.4668 11.1795 8.4668 15.3336C8.4668 19.4876 11.846 22.8668 16.0001 22.8668C20.1541 22.8668 23.5334 19.4876 23.5334 15.3336C23.5334 11.1795 20.1541 7.80029 16.0001 7.80029ZM13.7801 20.8961C13.735 20.8078 13.6912 20.717 13.6488 20.6238C13.0787 19.3715 12.7491 17.6902 12.6804 16.0003H10.0677L10.0677 16H12.6806C12.7544 17.8159 13.1295 19.6219 13.7805 20.8963L13.7801 20.8961ZM13.3728 20.4915C11.7464 19.6581 10.5791 18.076 10.2955 16.2003H12.4895C12.5703 17.7478 12.8675 19.2851 13.3728 20.4915ZM13.3729 10.1757C12.8676 11.3821 12.5704 12.9194 12.4895 14.4669H10.2956C10.5792 12.5912 11.7465 11.0091 13.3729 10.1757ZM11.1989 11.7717C11.7911 10.967 12.5798 10.3166 13.4984 9.89212C13.5911 9.84929 13.6851 9.80875 13.7804 9.7706C13.7352 9.85904 13.6914 9.95005 13.6488 10.0434C13.0788 11.2956 12.7492 12.9768 12.6805 14.6667H12.6807C12.7545 12.8507 13.1295 11.0447 13.7806 9.7703C12.741 10.1867 11.8509 10.8856 11.1989 11.7717ZM16.0001 21.1336C15.8977 21.1336 15.738 21.0597 15.5402 20.8295C15.3485 20.6063 15.1482 20.2667 14.9626 19.8162C14.6083 18.956 14.3195 17.7202 14.2366 16.2003H17.7636C17.6807 17.7202 17.3918 18.956 17.0375 19.8162C16.852 20.2667 16.6517 20.6063 16.4599 20.8295C16.2621 21.0597 16.1025 21.1336 16.0001 21.1336ZM14.027 16.0003L14.027 16H17.9736C17.8923 17.9631 17.4722 19.4923 16.9841 20.4005C17.4554 19.5234 17.8633 18.0671 17.9639 16.2003C17.9674 16.1342 17.9706 16.0675 17.9734 16.0003H14.027ZM17.9734 14.6667C17.9706 14.5996 17.9674 14.533 17.9639 14.4669C17.7941 11.315 16.7482 9.33366 16.0001 9.33366C15.7632 9.33366 15.4964 9.53233 15.2371 9.90392C15.4965 9.53214 15.7633 9.33337 16.0003 9.33337C16.7641 9.33337 17.8383 11.3988 17.9736 14.6667H17.9734ZM17.0375 10.851C17.3918 11.7112 17.6807 12.947 17.7636 14.4669H14.2366C14.3195 12.947 14.6083 11.7112 14.9626 10.851C15.1482 10.4006 15.3485 10.061 15.5402 9.83778C15.738 9.60757 15.8977 9.53366 16.0001 9.53366C16.1025 9.53366 16.2621 9.60757 16.4599 9.83778C16.6517 10.061 16.852 10.4006 17.0375 10.851ZM21.2867 18.1177C21.6239 17.4722 21.8477 16.7572 21.9329 16H19.32L19.3199 16.0003H21.9327C21.9251 16.0673 21.9165 16.134 21.9068 16.2003C21.807 16.8834 21.594 17.5294 21.2867 18.1177ZM21.7046 16.2003C21.421 18.076 20.2537 19.6581 18.6273 20.4916C19.1327 19.2852 19.4299 17.7478 19.5107 16.2003H21.7046ZM21.7046 14.4669H19.5107C19.4299 12.9194 19.1327 11.3821 18.6273 10.1757C20.2537 11.0092 21.4211 12.5913 21.7046 14.4669ZM18.2203 9.77077L18.22 9.7703C20.2306 10.5755 21.6822 12.4378 21.9329 14.6667H21.9326C21.9251 14.5997 21.9165 14.5332 21.9068 14.4669C21.6095 12.4316 20.3065 10.726 18.5018 9.89212C18.4093 9.84935 18.3154 9.80888 18.2203 9.77077ZM9.81325 11.4002C9.08762 12.5375 8.6668 13.8873 8.6668 15.3336C8.6668 19.3772 11.9565 22.6668 16.0001 22.6668C18.416 22.6668 20.5627 21.4926 21.8996 19.6848C20.5627 21.4924 18.416 22.6665 16.0003 22.6665C11.9567 22.6665 8.66699 19.3769 8.66699 15.3333C8.66699 13.8872 9.08774 12.5375 9.81325 11.4002ZM20.7128 25.9995V25.9995C20.7127 25.5205 20.3251 25.1328 19.8461 25.1328H11.8462C11.3672 25.1328 10.9795 25.5205 10.9795 25.9995C10.9795 26.4785 11.3672 26.8662 11.8462 26.8662H19.8461C20.3251 26.8662 20.7128 26.4785 20.7128 25.9995ZM20.3163 25.5266C20.1959 25.4069 20.0298 25.333 19.8463 25.333H11.8464C11.4779 25.333 11.1797 25.6312 11.1797 25.9997C11.1797 26.1838 11.2542 26.3504 11.3746 26.471C11.254 26.3504 11.1795 26.1837 11.1795 25.9995C11.1795 25.631 11.4777 25.3328 11.8462 25.3328H19.8461C20.0297 25.3328 20.1958 25.4068 20.3163 25.5266Z" fill="white" />
    </svg>
  )
}
