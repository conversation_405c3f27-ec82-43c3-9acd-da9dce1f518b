import React, { useContext } from 'react';
import { useAppContext } from '../../hooks';
import Tick from '../Icons/Tick';
import { isEmpty } from '../../helpers';
import { LanguageContext } from '../../context/LanguageProvider';

type Props = {};

export default function QRCode({}: Props) {
  const { dataCapture } = useAppContext();
  //get data context language
  const { dictionary } = useContext(LanguageContext);
  const result = dictionary['result']['QR'];

  const resultQR: string = dataCapture.QRscan ? dataCapture.QRscan : '';

  const numberCardId = !isEmpty(dataCapture.orc.object) ? dataCapture.orc.object.id : '_';
  const fullName = !isEmpty(dataCapture.orc.object) ? dataCapture.orc.object.name : '_';
  const dob = !isEmpty(dataCapture.orc.object) ? dataCapture.orc.object.birth_day : '_';

  return (
    <div className="vnpt-flex vnpt-flex-col vnpt-gap-3">
      <div>
        <p className="vnpt-font-inter vnpt-text-sm sm:vnpt-text-base vnpt-font-normal vnpt-text-gray-900">
          {result['infor']}
        </p>
      </div>
      <div className="vnpt-p-4 vnpt-bg-result-bg vnpt-rounded">
        <p className="vnpt-text-gray-900 vnpt-font-open sm:vnpt-text-base vnpt-font-semibold vnpt-text-sm">
          {dataCapture.QRscan || '_'}
        </p>
      </div>
      <div className="vnpt-flex vnpt-flex-col vnpt-gap-3 vnpt-pb-5">
        <p className="vnpt-font-inter vnpt-text-sm vnpt-font-normal sm:vnpt-text-base vnpt-text-gray-900">
          {result['QR_des']}
        </p>
        <div className="vnpt-flex vnpt-flex-col vnpt-gap-3">
          <div className="vnpt-flex vnpt-items-center vnpt-justify-between">
            <p className="vnpt-font-normal vnpt-text-gray-600 vnpt-font-inter vnpt-text-sm sm:vnpt-text-base">
              {result['des_1']}
            </p>
            <p className="vnpt-text-gray-900 vnpt-font-open vnpt-font-semibold vnpt-text-sm sm:vnpt-text-base">
              {numberCardId}
            </p>
            {resultQR.includes(numberCardId) && <Tick />}
          </div>
          <div className="vnpt-flex vnpt-items-center vnpt-justify-between">
            <p className="vnpt-font-normal vnpt-text-gray-600 vnpt-font-inter vnpt-text-sm sm:vnpt-text-base">
              {result['des_2']}
            </p>
            <p className="vnpt-text-gray-900 vnpt-font-open vnpt-font-semibold vnpt-text-sm sm:vnpt-text-base">
              {fullName}
            </p>
            {resultQR.toLocaleUpperCase().includes(fullName) && <Tick />}
          </div>
          <div className="vnpt-flex vnpt-items-center vnpt-justify-between">
            <p className="vnpt-font-normal vnpt-text-gray-600 vnpt-font-inter vnpt-text-sm sm:vnpt-text-base">
              {result['des_3']}
            </p>
            <p className="vnpt-text-gray-900 vnpt-font-open vnpt-font-semibold vnpt-text-sm sm:vnpt-text-base">{dob}</p>
            {resultQR.includes(dob.split('/').join('')) && <Tick />}
          </div>
        </div>
      </div>
    </div>
  );
}
