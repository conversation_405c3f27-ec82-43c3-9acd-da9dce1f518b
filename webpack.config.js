const path = require('path');
const autoprefixer = require('autoprefixer');

module.exports = {
  // <PERSON><PERSON><PERSON> c<PERSON>u hình khác của bạn...
  entry: {
    build: './src/index.ts',
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'web-sdk-version-3.1.0.0.js',
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.json'],
  },
  mode: 'production',
  target: 'web',
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        loader: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.scss$/,
        use: [
          'style-loader', // 3. Injects styles into the DOM
          {
            loader: 'css-loader', // 2. Turns CSS into CommonJS
            options: {
              url: false, // Disable processing of URLs, so Webpack doesn't bundle them
            },
          },
          {
            loader: 'postcss-loader', // 1. Processes CSS with PostCSS
            options: {
              postcssOptions: {
                plugins: [
                  require('autoprefixer'),
                  require('tailwindcss'), // Make sure to include TailwindCSS here if you haven't already
                  // any other PostCSS plugins you want to include
                ],
              },
            },
          },
          'sass-loader', // 0. Compiles Sass to CSS
        ],
      },
    ],
  },
  watch: true,
  cache: {
    type: 'filesystem', // Sử dụng bộ nhớ đệm trên hệ thống tệp
  },
  devServer: {
    static: {
      directory: path.join(__dirname, 'dist'), // Serve static files from 'dist'
    },
    compress: true,
    port: 4200,
  },
};
