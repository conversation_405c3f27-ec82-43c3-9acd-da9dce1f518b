export type FakeCamLabel = string | string[] | RegExp | RegExp[];

export type ControlWebcamProps = {
    videoRef: HTMLVideoElement | null,
    cb?: () => void,
    streams: MediaStream[],
    turnOnFrontCam?: boolean,
    fakeCamLabel?: FakeCamLabel
}
export type ConstraintsProps = {
    audio: boolean;
    video: {
        facingMode: string;
        aspectRatio: number;
        width: {
            ideal: number;
        };
        height: {
            ideal: number;
        };
        deviceId: string | null;
    }
} | {
    video: {
        facingMode: string;
        width: {
            ideal: number;
        };
        aspectRatio: number;
        height?: undefined;
        deviceId?: undefined;
    };
    audio: boolean;
}

export interface ItemComponentProps {
    id: number;
    name: string;
    action?: (item: ItemComponentProps) => void;
    selectedId: number;
    icon?: string | React.JSX.Element;
}


// language context
export type ContextTypeLanguage = {
    userLanguage: string;
    dictionary: any;
    userLanguageChange: (language: string) => void;
};

export type TYPE_DOCUMENT = 'one_side' | 'two_side'
export type DocumentProps = {
    id: number,
    name: string,
    icon: string | React.JSX.Element;
}

export interface UpLoadImageProps {
    data: string
    successCallback: () => void
    errorCallback: () => void
    configData: any
}

export type SCREEN = 'LIST_DOCUMENT' | 'PRERARE_CAPTURE' | 'CAPTURE_ONE_SIDE' | 'CAPTURE_TWO_SIDE' | 'VALIDATIONS_RESULTS' | 'FACE_VERIFY' | 'QR_SCAN' | 'QR_UPLOAD';
export type captureProps = {
    onNextResult: () => void;
    setIsShowModal: React.Dispatch<React.SetStateAction<boolean>>
}

type DataResponseUploadImage = {
    description: string,
    fileName: string,
    fileType: string,
    hash: string,
    storageType: string,
    title: string,
    tokenId: string,
    uploadedDate: string,
}
export interface ResponseUploadImage {
    message: string
    object: DataResponseUploadImage
}
export interface ResponseDataCapture {
    hash_img: string,
    type_document?: number;
    data_hash_document: { img_front: string, img_back?: string },
    base64_doc_img: {
        img_front: string,
        img_back?: string,
        far_img?: string,
        near_img?: string
    },
    base64_face_img: {
        img_face_far: string,
        img_face_near: string,
    },
    client_session: string,
    liveness_card_front: {
        dataBase64: string,
        object: {
            blur_face: string,
            face_swapping: boolean | null,
            face_swapping_prob: number | null,
            fake_liveness: boolean | null,
            fake_liveness_prob: number | null,
            fake_print_photo: boolean | null,
            fake_print_photo_prob: number | null,
            liveness: string,
            liveness_msg: string
        }
    };
    liveness_card_back: {
        dataBase64: string,
        object: {
            blur_face: string,
            face_swapping: boolean | null,
            face_swapping_prob: number | null,
            fake_liveness: boolean | null,
            fake_liveness_prob: number | null,
            fake_print_photo: boolean | null,
            fake_print_photo_prob: number | null,
            liveness: string,
            liveness_msg: string
        }
    };
    orc: {
        object: {
            back_type_id: number | null,
            warning_msg: string,
            tampering: {
                warning: string[]
            },
            birth_day: string,
            birth_day_prob: number | null,
            card_type: string,
            checking_result_front: {
                check_photocopied_prob: number | null,
                check_photocopied_result: string,
                corner_cut_prob: [],
                corner_cut_result: string,
                edited_prob: number | null,
                edited_result: string,
                recaptured_prob: number | null,
                recaptured_result: string,
            },
            id_probs: string,
            citizen_id: string,
            citizen_id_prob: number | null,
            corner_warning: string,
            expire_warning: string,
            gender: string,
            gender_prob: number | null,
            id: string,
            id_fake_prob: number | null,
            id_fake_warning: string,
            issue_date: string,
            issue_date_prob: number | null,
            issue_place: string,
            issue_place_prob: number | null,
            msg: string,
            name: string,
            name_prob: number | null,
            nation_policy: string,
            nation_slogan: string,
            nationality: string,
            quality_front: {
                blur_score: number | null,
                bright_spot_score: number | null
            },
            rank: string,
            recent_location: string,
            recent_location_prob: number | null,
            type_id: number | null,
            valid_date: string,
            valid_date_prob: number | null,
            warning: string[],
            general_warning: string[];
        }
    };
    compare: {
        object: {
            match_warning: string;
            msg: string;
            multiple_faces: string;
            prob: number | null;
            result: string;
        }
    };
    liveness_face: {
        object: {
            age: number | null,
            blur_face: string,
            blur_face_score: number | null;
            gender: string,
            is_eye_open: string,
            liveness: string,
            liveness_msg: string,
            liveness_prob: number | null
        }
    };
    maskedFaceRes: {
        object: {
            masked: string
        }
    };
    QRscan: any,
    othersData: {
        step: string
    };
    videoRecorded?: Blob | null
}

export interface responseDataModal {
    liveness_face: any,
    compare: any,
    masked: any
}

export type IDataHash = {
    hashFront: string,
    hashBack: string
}
export type Ifield = {
    title: string
    value: string | number | React.ReactElement
}
export type OrcType = { hash_front: string, hash_back: string, typeDocument: number }

export type FLOW_TYPE = 'DOCUMENT' | 'FACE' | 'BOTH' | null

export interface dataConfigType {
    // configs for api
    BACKEND_URL: string,
    TOKEN_KEY: string,
    TOKEN_ID: string,
    ACCESS_TOKEN: string,
    CALL_BACK_END_FLOW: (result: any) => Promise<void>,
    CALL_BACK_DOCUMENT_RESULT: (document_result: any) => Promise<void>,
    HEADERS_REQUEST: Record<string, string>,

    // config endpoint
    ENDPOINT_UPLOAD_IMAGE: string,
    ENDPOINT_LIVENESS_CARD: string,
    ENDPOINT_LIVENESS_FACE: string,
    ENDPOINT_MASKED_FACE: string,
    ENDPOINT_COMPARE_FACE: string,
    ENDPOINT_OCR_DOCUMENT: string,
    ENDPOINT_OCR_DOCUMENT_FRONT: string,

    // config api calls
    CHECK_LIVENESS_CARD: boolean,
    CHECK_LIVENESS_FACE: boolean,
    CHECK_MASKED_FACE: boolean,
    COMPARE_FACE: boolean,
    OCR_DOCUMENT: boolean,

    // configs for flow sdk
    FLOW_TAKEN: FLOW_TYPE,
    DOCUMENT_TYPE_START: number,
    LIST_TYPE_DOCUMENT: number[];
    CHALLENGE_CODE: string,
    HAS_QR_SCAN: boolean,
    DEFAULT_LANGUAGE: string,
    DOUBLE_LIVENESS: boolean;
    FAKE_CAM_LABEL: FakeCamLabel;
    USE_METHOD: 'PHOTO' | 'UPLOAD' | 'BOTH';
    URL_WEB_OVAL: string;
    URL_MOBILE_OVAL: string;
    URL_ENGLISH_VIDEO_TUTORIAL: string;
    URL_VIETNAMESE_VIDEO_TUTORIAL: string;
    MAX_SIZE_IMAGE: number;
    TIME_EXCEED_FACE_LIVENESS: number;

    // configs for UI
    LINK_ICON_CCCD: string,
    LINK_ICON_PASSPORT: string,
    LINK_ICON_DRIVER_LICENSE: string,
    LINK_ICON_OTHER_PAPERS: string,
    LINK_ICON_QR_SCAN: string,

    // result UI
    HAS_RESULT_SCREEN: boolean,
    SHOW_TAB_RESULT_INFORMATION: boolean,
    SHOW_TAB_RESULT_VALIDATION: boolean,
    SHOW_TAB_RESULT_QRCODE: boolean,


    // common UI
    HAS_BACKGROUND_IMAGE: boolean;
    SHOW_STEP: boolean,
    CUSTOM_THEME: {
        PRIMARY_COLOR: string,
        TEXT_COLOR_DEFAULT: string,
        BACKGROUND_COLOR: string
    }
}
export interface AppContextData {
    dataCapture: ResponseDataCapture;
    setDataCapture: React.Dispatch<ResponseDataCapture>,
    dataConfig: dataConfigType,
    setDataConfig: React.Dispatch<dataConfigType>,
    setScreenShow: React.Dispatch<SCREEN>,
    screenShow: SCREEN
}

export type ModalType = 'instruction' | 'verify' | 'result';

