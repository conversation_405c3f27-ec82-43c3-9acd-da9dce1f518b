import React from 'react';

type Props = {};

export default function ButtonCamera({}: Props) {
  return (
    <svg width="76" height="76" viewBox="0 0 76 76" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_d_6514_2634)">
        <path
          d="M70 34C70 51.6731 55.6731 66 38 66C20.3269 66 6 51.6731 6 34C6 16.3269 20.3269 2 38 2C55.6731 2 70 16.3269 70 34Z"
          stroke="#184693"
          stroke-width="4"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M66 34C66 49.464 53.464 62 38 62C22.536 62 10 49.464 10 34C10 18.536 22.536 6 38 6C53.464 6 66 18.536 66 34ZM47.1304 27.3391H44.6261L43.2435 25.2522C42.7217 24.4696 41.8087 24 40.8696 24H35.1304C34.1913 24 33.2783 24.4696 32.7565 25.2522L31.3739 27.3391H28.8696C27.2783 27.3391 26 28.6174 26 30.2087V40.3304C26 41.9217 27.2783 43.2 28.8696 43.2H47.1304C48.7217 43.2 50 41.9217 50 40.3304V30.2087C50 28.6174 48.7217 27.3391 47.1304 27.3391ZM38 40.8522C34.4261 40.8522 31.5304 37.9565 31.5304 34.3826C31.5304 30.8087 34.4261 27.9391 38 27.9391C41.5739 27.9391 44.4696 30.8348 44.4696 34.4087C44.4696 37.9565 41.5739 40.8522 38 40.8522ZM38 29.6C35.3565 29.6 33.2 31.7644 33.2 34.4175C33.2 37.0705 35.3565 39.2 38 39.2C40.6435 39.2 42.8 37.0356 42.8 34.3825C42.8 31.7295 40.6435 29.6 38 29.6Z"
          fill="#184693"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_6514_2634"
          x="0"
          y="0"
          width="76"
          height="76"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0.0941176 0 0 0 0 0.27451 0 0 0 0 0.576471 0 0 0 0.25 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6514_2634" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6514_2634" result="shape" />
        </filter>
      </defs>
    </svg>
  );
}
