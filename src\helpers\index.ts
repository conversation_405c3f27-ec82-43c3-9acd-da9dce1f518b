
export function b64toBlob(dataURI: string) {
    var byteString = atob(dataURI.split(',')[1]);
    var ab = new ArrayBuffer(byteString.length);
    var ia = new Uint8Array(ab);

    for (var i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
    }
    return new Blob([ab], { type: 'image/jpeg' });
}

//format date
export function formatDate(date: Date) {
    var d: Date = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2)
        month = '0' + month;
    if (day.length < 2)
        day = '0' + day;

    return [year, month, day].join('-');
}


export function isEmpty(data: any) {
    if (typeof (data) === 'object') {
        if (JSON.stringify(data) === '{}' || JSON.stringify(data) === '[]') {
            return true;
        } else if (!data) {
            return true;
        }
        return false;
    } else if (typeof (data) === 'string') {
        if (!data.trim()) {
            return true;
        }
        return false;
    } else if (typeof (data) === 'undefined') {
        return true;
    } else {
        return false;
    }
}

export function handleReloadAndScrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};
export function removeScriptBySrc(src: string) {
    const scriptTags = document.querySelectorAll(`script[src="${src}"]`);

    scriptTags.forEach((scriptTag) => {
        if (scriptTag.parentElement) {
            scriptTag.parentElement.removeChild(scriptTag);
        }
    });
}
