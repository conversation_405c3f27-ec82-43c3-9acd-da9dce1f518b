import React from 'react'

type Props = {}

export default function Upload({ }: Props) {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_7950_30120)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9.99996 0.633301C9.72596 0.633301 9.46317 0.742127 9.26939 0.935847L4.26935 5.93585L4.26694 5.93835C4.07871 6.13324 3.97455 6.39426 3.97691 6.66519C3.97926 6.93613 4.08794 7.1953 4.27952 7.38689C4.47111 7.57848 4.73029 7.68716 5.00122 7.68951C5.27216 7.69186 5.5332 7.58773 5.72809 7.3995L8.96663 4.16097V14.1665C8.96663 14.4405 9.0755 14.7033 9.26928 14.8971C9.46307 15.0909 9.7259 15.1998 9.99996 15.1998C10.274 15.1998 10.5368 15.0909 10.7306 14.8971C10.9244 14.7033 11.0333 14.4405 11.0333 14.1665V4.16097L14.2694 7.39707L14.2719 7.39948C14.4667 7.58771 14.7278 7.69186 14.9987 7.68951C15.2696 7.68716 15.5288 7.57848 15.7204 7.38689C15.912 7.1953 16.0207 6.93613 16.023 6.66519C16.0254 6.39426 15.9212 6.13321 15.733 5.93833L10.7305 0.935847C10.5367 0.742127 10.274 0.633301 9.99996 0.633301ZM1.66663 10.6333C1.39258 10.6333 1.12974 10.7422 0.935957 10.936C0.742169 11.1297 0.633301 11.3926 0.633301 11.6666V15.1777C0.634459 16.2883 1.07617 17.3531 1.8615 18.1384C2.64683 18.9238 3.71184 19.3655 4.82247 19.3666H15.1777C16.2883 19.3655 17.3531 18.9238 18.1384 18.1384C18.9238 17.3531 19.3655 16.2881 19.3666 15.1775V11.6666C19.3666 11.3926 19.2578 11.1297 19.064 10.936C18.8702 10.7422 18.6074 10.6333 18.3333 10.6333C18.0592 10.6333 17.7964 10.7422 17.6026 10.936C17.4088 11.1297 17.3 11.3926 17.3 11.6666V15.1773C17.2994 15.74 17.0755 16.2796 16.6776 16.6776C16.2796 17.0755 15.74 17.2994 15.1773 17.3H4.82268C4.25989 17.2994 3.72032 17.0755 3.32236 16.6776C2.92443 16.2796 2.7006 15.74 2.69997 15.1773L2.69997 11.6666C2.69997 11.3926 2.5911 11.1297 2.39731 10.936C2.20352 10.7422 1.94069 10.6333 1.66663 10.6333Z" fill="#111127" />
            </g>
            <defs>
                <clipPath id="clip0_7950_30120">
                    <rect width="20" height="20" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}
