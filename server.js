var express = require('express');
var cors = require('cors');
var path = require('path');
var app = express();

app.use(cors());
app.use(express.static('dist'));
app.use(express.static('assets'));
app.get('/products/:id', function (req, res, next) {
  res.json({ msg: 'This is CORS-enabled for all origins!' });
});

app.listen(4200, function () {
  console.log('CORS-enabled web server listening on port 4200');
});
