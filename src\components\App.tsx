import React from 'react';
import { AppContextProvider } from '../context/AppProvider';
import LanguageProvider from '../context/LanguageProvider';
import '../styles/index.scss';
import { dataConfigType } from '../types';
import ListDocument from './ListDocument';
import Wrapper from './Wrapper';

export const App = (dataConfig: dataConfigType) => {
  return (
    <div className={`${dataConfig.HAS_BACKGROUND_IMAGE ? 'bg-hero' : ''} bg-sdk vnpt-ekyc-sdk-container`}>
      <AppContextProvider>
        <LanguageProvider>
          <Wrapper dataConfig={dataConfig}>
            <div className="vnpt-mx-auto vnpt-px-4 sm:vnpt-p-0 vnpt-py-4">
              <ListDocument />
            </div>
          </Wrapper>
        </LanguageProvider>
      </AppContextProvider>
    </div>
  );
};
