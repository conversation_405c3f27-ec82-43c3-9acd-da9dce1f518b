import React, { useEffect } from 'react';
import { initDataConfig } from '../../context/AppProvider';
import { useAppContext } from '../../hooks';
import { dataConfigType } from '../../types';

type Props = {
  children: any;
  dataConfig: dataConfigType;
};

export default function Wrapper({ children, dataConfig }: Props) {
  const { setDataConfig } = useAppContext();
  const mergedConfig = { ...initDataConfig, ...dataConfig };

  useEffect(() => {
    setDataConfig(mergedConfig);
  }, []);

  return <div>{children}</div>;
}
