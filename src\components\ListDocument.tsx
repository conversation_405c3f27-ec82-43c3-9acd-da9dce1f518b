import React, { useCallback, useContext, useEffect, useState } from 'react';
import { livenessCardMobile, ocrFullSideMobile, ocrOneSideMobile, uploadImage } from '../api';
import { LIST_DOCUMENT_ID, VERSION } from '../constants';
import { LanguageContext } from '../context/LanguageProvider';
import { b64toBlob } from '../helpers';
import { useAppContext } from '../hooks';
import { DocumentProps, FLOW_TYPE, IDataHash, ModalType, OrcType, ResponseUploadImage, TYPE_DOCUMENT } from '../types';
import { changeThemeDefault, defineTransaction, onShowDocumentResult, onShowResult } from '../utils';
import CaptureOneSide from './CaptureOneSide';
import CaptureTwoSide from './CaptureTwoSide';
import FaceVerify from './FaceVerifyV2';
import CardID from './Icons/CardID';
import License from './Icons/License';
import OtherPapers from './Icons/OtherPapers';
import Passport from './Icons/Passport';
import Intruction from './InstructionModals/Instruction';
import Loading from './Loading';
import MenuOptions from './MenuOptions';
import QRscan from './QRscanner/ScanQR';
import QRCodeUpload from './QRscanner/UploadQR';
import ValidationResults from './ValidationResults';

const ListDocument: React.FC = () => {
  const [isShowModal, setIsShowModal] = useState<boolean>(false);
  const [nameModal, setNameModal] = useState<ModalType>('result');
  const [isLoading, setLoading] = useState<boolean>(false);
  const [selectedDocumentID, setSelectedDocumentID] = useState<number | null>(null);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const [dataHash, setDataHash] = useState<IDataHash>({
    hashFront: '',
    hashBack: '',
  });
  const { dataCapture, setDataCapture, dataConfig, setScreenShow, screenShow } = useAppContext();

  // get flow ekyc from config data
  const flow: FLOW_TYPE = dataConfig.FLOW_TAKEN;

  //get data context language
  const { dictionary } = useContext(LanguageContext);
  const listTitle = dictionary['home'];
  const title = listTitle['title'];
  const subTitle = listTitle['subTitle'];

  const client_session = defineTransaction(VERSION);

  const documents: DocumentProps[] = [
    {
      id: LIST_DOCUMENT_ID.CITIZEN_CARD_ID_OLD,
      name: listTitle['cmt'],
      icon: dataConfig.LINK_ICON_CCCD ? <img src={dataConfig.LINK_ICON_CCCD} alt="icon-cccd" /> : <CardID />,
    },
    {
      id: LIST_DOCUMENT_ID.PASSPORT_ID,
      name: listTitle['passpost'],
      icon: dataConfig.LINK_ICON_PASSPORT ? (
        <img src={dataConfig.LINK_ICON_PASSPORT} alt="icon-passport" />
      ) : (
        <Passport />
      ),
    },
    {
      id: LIST_DOCUMENT_ID.DRIVE_LICENSE_ID,
      name: listTitle['license'],
      icon: dataConfig.LINK_ICON_DRIVER_LICENSE ? (
        <img src={dataConfig.LINK_ICON_DRIVER_LICENSE} alt="icon-license" />
      ) : (
        <License />
      ),
    },
    {
      id: LIST_DOCUMENT_ID.AUTO_DETECT_DOCUMENT,
      name: listTitle['other_papers'],
      icon: dataConfig.LINK_ICON_OTHER_PAPERS ? (
        <img src={dataConfig.LINK_ICON_OTHER_PAPERS} alt="icon-other-papers" />
      ) : (
        <OtherPapers />
      ),
    },
    {
      id: LIST_DOCUMENT_ID.CITIZEN_CARD_ID_CHIP,
      name: listTitle['cmt_chip'],
      icon: dataConfig.LINK_ICON_CCCD ? <img src={dataConfig.LINK_ICON_CCCD} alt="icon-cccd" /> : <CardID />,
    },
  ];

  // filter list document will be display
  const documentIds: DocumentProps[] = documents.filter((item) => dataConfig.LIST_TYPE_DOCUMENT.includes(item.id));

  const handleChooseDocument = useCallback((document: DocumentProps) => {
    // setIsShowModal(true);
    setSelectedDocumentID(document.id);
    dataCapture.type_document = document.id;
  }, []);

  function getDocumentSelectedType(): TYPE_DOCUMENT {
    switch (selectedDocumentID) {
      case LIST_DOCUMENT_ID.AUTO_DETECT_DOCUMENT:
      case LIST_DOCUMENT_ID.CITIZEN_CARD_ID_CHIP:
      case LIST_DOCUMENT_ID.CITIZEN_CARD_ID_NEW:
      case LIST_DOCUMENT_ID.CITIZEN_CARD_ID_OLD:
        return 'two_side';
      default:
        return 'one_side';
    }
  }

  async function handleShowResults() {
    setLoading(true);
    // check oneside document and call api
    if (dataCapture.base64_doc_img.img_front && !dataCapture.base64_doc_img.img_back) {
      try {
        //convert to blob to upload image
        const blobFont = b64toBlob(dataCapture.base64_doc_img.img_front);
        const resultUploadImage: ResponseUploadImage = await uploadImage(blobFont, dataConfig);

        const data = {
          hash_front: resultUploadImage.object.hash,
          typeDocument: selectedDocumentID,
        };
        setDataHash({ ...dataHash, hashFront: resultUploadImage.object.hash });
        const promise1 = dataConfig.CHECK_LIVENESS_CARD
          ? livenessCardMobile(data.hash_front, dataConfig, client_session)
          : Promise.resolve(false);
        const promise2 = dataConfig.OCR_DOCUMENT
          ? ocrOneSideMobile(data, dataConfig, client_session)
          : Promise.resolve(false);
        const allPromise = Promise.all([promise1, promise2]);
        try {
          const resultsCapture = await allPromise;
          const dataUpdated = {
            ...dataCapture,
            data_hash_document: { img_front: data.hash_front },
            client_session: client_session,
            liveness_card_front: resultsCapture[0],
            orc: resultsCapture[1],
          };

          setDataCapture(dataUpdated);

          if (flow === 'BOTH') {
            await onShowDocumentResult(dataConfig, dataUpdated);
            setLoading(false);
            setNameModal('verify');
            // setIsShowModal(true);
            setScreenShow('FACE_VERIFY');
          }
          if (flow === 'DOCUMENT') {
            setLoading(false);
            if (dataConfig.HAS_RESULT_SCREEN) {
              setScreenShow('VALIDATIONS_RESULTS');
            } else {
              onShowResult(dataConfig, dataUpdated);
            }
          }
        } catch (error) {
          setIsShowModal(true);
          console.log(error);
        }
      } catch (error) {
        setIsShowModal(true);
        console.log('🚀 ~ handleShowResults ~ error:', error);
      }
    }

    // check twoside document and call api
    if (dataCapture.base64_doc_img.img_front && dataCapture.base64_doc_img.img_back) {
      try {
        //convert to blob to upload image
        const blobFont = b64toBlob(dataCapture.base64_doc_img.img_front);
        const blobBack = b64toBlob(dataCapture.base64_doc_img.img_back);

        const promisesUpload = Promise.all<ResponseUploadImage>([
          uploadImage(blobFont, dataConfig),
          uploadImage(blobBack, dataConfig),
        ]);
        try {
          const [resultUploadImageFront, resultUploadImageBack] = await promisesUpload;
          const data: OrcType = {
            hash_front: resultUploadImageFront.object.hash,
            hash_back: resultUploadImageBack.object.hash,
            typeDocument: selectedDocumentID || NaN,
          };
          setDataHash({
            ...dataHash,
            hashFront: resultUploadImageFront.object.hash,
            hashBack: resultUploadImageBack.object.hash,
          });
          const promise1 = dataConfig.CHECK_LIVENESS_CARD
            ? livenessCardMobile(data.hash_front, dataConfig, client_session)
            : Promise.resolve(false);
          const promise2 = dataConfig.CHECK_LIVENESS_CARD
            ? livenessCardMobile(data.hash_back, dataConfig, client_session)
            : Promise.resolve(false);
          const promise3 = dataConfig.OCR_DOCUMENT
            ? ocrFullSideMobile(data, dataConfig, client_session)
            : Promise.resolve(false);
          const allPromise = Promise.all([promise1, promise2, promise3]);

          const resultsCapture = await allPromise;
          const dataUpdated = {
            ...dataCapture,
            data_hash_document: {
              img_front: data.hash_front,
              img_back: data.hash_back,
            },
            liveness_card_front: resultsCapture[0],
            liveness_card_back: resultsCapture[1],
            orc: resultsCapture[2],
          };

          setDataCapture(dataUpdated);

          if (flow === 'BOTH') {
            await onShowDocumentResult(dataConfig, dataUpdated);
            setLoading(false);
            setNameModal('verify');
            // setIsShowModal(true);
            setScreenShow('FACE_VERIFY');
          }
          if (flow === 'DOCUMENT') {
            setLoading(false);
            if (dataConfig.HAS_RESULT_SCREEN) {
              setScreenShow('VALIDATIONS_RESULTS');
            } else {
              onShowResult(dataConfig, dataUpdated);
            }
          }
          if (flow === 'FACE') {
            setLoading(false);
            if (dataConfig.HAS_RESULT_SCREEN) {
              setScreenShow('VALIDATIONS_RESULTS');
            } else {
              onShowResult(dataConfig, dataUpdated);
            }
          }
        } catch (error) {
          console.log(error);
          setIsShowModal(true);
        }
      } catch (error) {
        console.log('🚀 ~ handleShowResults ~ error:', error);
        setIsShowModal(true);
      }
    }
  }

  function checkScreenShow() {
    // setScreenShow('FACE_VERIFY');
    // return;
    if (selectedDocumentID === null) {
      setScreenShow('LIST_DOCUMENT');
      return;
    }
    // start check flow
    if (flow === 'FACE') {
      setNameModal('verify');
      setScreenShow('FACE_VERIFY');
      return;
    }
    // end check flow
    if (getDocumentSelectedType() === 'one_side') {
      setScreenShow('CAPTURE_ONE_SIDE');
    }
    if (getDocumentSelectedType() === 'two_side') {
      setScreenShow('CAPTURE_TWO_SIDE');
    }
  }

  const resetAction = useCallback(() => {
    window.location.reload();
    // handleReloadAndScrollToTop();
    // setDataCapture(initResponseDataCapture);
    // setScreenShow('LIST_DOCUMENT');
    // setIsModalOpen(false);
    // setNameModal('result');
    // setLoading(false);
    // if (dataConfig.DOCUMENT_TYPE_START !== 999) {
    //   setSelectedDocumentID(dataConfig.DOCUMENT_TYPE_START);
    // } else {
    //   setSelectedDocumentID(null);
    // }
  }, []);

  const [sdk, setSdk] = useState<any>();
  useEffect(() => {
    const getSDK = async () => {
      const sdk = await (window as any).FaceVNPTBrowserSDK;
      sdk.init();
      setSdk(sdk);
    };
    if (dataCapture.base64_doc_img.img_front) {
      getSDK();
    }
  }, [dataCapture.base64_doc_img.img_front]);

  useEffect(() => {
    checkScreenShow();
  }, [selectedDocumentID]);

  useEffect(() => {
    if (dataConfig.DOCUMENT_TYPE_START !== 999) {
      // setIsShowModal(true);
      setSelectedDocumentID(dataConfig.DOCUMENT_TYPE_START);
      dataCapture.type_document = dataConfig.DOCUMENT_TYPE_START;
    }
  }, [dataConfig.DOCUMENT_TYPE_START]);

  if (flow === null) return <></>;

  if (isShowModal)
    return (
      <div className="vnpt-mt-12">
        <Intruction
          isShowModal={isShowModal}
          resetAction={resetAction}
          setIsShowModal={setIsShowModal}
          nameModal={nameModal}
          selectedDocumentID={selectedDocumentID}
        />
      </div>
    );

  return (
    <div>
      <div>
        <style dangerouslySetInnerHTML={changeThemeDefault(dataConfig)} />
      </div>
      {isLoading ? (
        <div className="vnpt-mx-auto vnpt-mt-52 sm:vnpt-w-96 vnpt-flex vnpt-justify-center vnpt-items-center">
          <Loading />
        </div>
      ) : (
        <>
          {/* {screenShow === 'PRERARE_CAPTURE' && (
            <PrepareCapture setIsShowModal={setIsShowModal} typeDocument={getDocumentSelectedType()} />
          )} */}
          {screenShow === 'CAPTURE_ONE_SIDE' && (
            <CaptureOneSide onNextResult={handleShowResults} setIsShowModal={setIsShowModal} />
          )}
          {screenShow === 'CAPTURE_TWO_SIDE' && (
            <CaptureTwoSide onNextResult={handleShowResults} setIsShowModal={setIsShowModal} />
          )}
          {screenShow === 'FACE_VERIFY' && (
            <FaceVerify
              setNameModal={setNameModal}
              dataHash={dataHash}
              setIsShowModal={setIsShowModal}
              typeDocument={getDocumentSelectedType()}
            />
          )}
          {screenShow === 'VALIDATIONS_RESULTS' && (
            <ValidationResults resetAction={resetAction} typeDocument={getDocumentSelectedType()} />
          )}
          {screenShow === 'QR_UPLOAD' && <QRCodeUpload setIsShowModal={setIsShowModal} />}
          {screenShow === 'QR_SCAN' && <QRscan setIsShowModal={setIsShowModal} />}
        </>
      )}
    </div>
  );
};

export default ListDocument;
