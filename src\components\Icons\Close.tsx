import React from 'react'

type Props = {}

export default function Close({ }: Props) {
    return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="12" fill="#CBCBCB" />
            <g clip-path="url(#clip0_7950_26525)">
                <path d="M16.9503 7.05005L7.05029 16.95" stroke="white" stroke-width="3" stroke-miterlimit="10" stroke-linecap="square" />
                <path d="M16.9503 16.95L7.05029 7.05005" stroke="white" stroke-width="3" stroke-miterlimit="10" stroke-linecap="square" />
            </g>
            <defs>
                <clipPath id="clip0_7950_26525">
                    <rect width="14.4" height="14.4" fill="white" transform="translate(4.80029 4.80005)" />
                </clipPath>
            </defs>
        </svg>
    )
}
